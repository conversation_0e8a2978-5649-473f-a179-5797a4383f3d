/**
 * Basic user management. Handles creation and tracking of proxy users, personal
 * access tokens, and quota management. Supports in-memory and Firebase Realtime
 * Database persistence stores.
 *
 * Users are identified solely by their personal access token. The token is
 * used to authenticate the user for all proxied requests.
 */

import crypto from "crypto";
import { and, eq, inArray, isNull, sql, type ExtractTablesWithRelations } from "drizzle-orm";
import type { SQLiteColumn, SQLiteTransaction } from "drizzle-orm/sqlite-core";
import schedule from "node-schedule";
import superjson from "superjson";
import { v4 as uuid } from "uuid";

import { config } from "@/config";
import { logger } from "@/logger";

import { getDatabase } from "../database";
import { schema } from "../database/database";
import { eventLogger } from "../logging";
import { getModelFamilyForService, type LLMService } from "../models";
import { getTokenCostUsd } from "../stats";

const log = logger.child({ module: "users" });

export type User = typeof schema.users.$inferSelect;
export type DB =
  | ReturnType<typeof getDatabase>
  | SQLiteTransaction<"sync", void, typeof schema, ExtractTablesWithRelations<typeof schema>>;

let cronJob: schedule.Job | null = null;

export async function init() {
  log.info({ store: config.gatekeeperStore }, "Initializing user store...");
  if (config.gatekeeperStore !== "memory") await initUserDatabase();

  cronJob = schedule.scheduleJob("0 * * * *", runCronJobs);
  log.info("User store initialized.");
}

async function runCronJobs() {
  await Promise.allSettled([cleanUpExpiredUsers(), resetUserQuotas()]);
  log.info({ next: cronJob?.nextInvocation() }, "Cron jobs complete. Next run scheduled.");
}

/**
 * Creates a new user and returns their token. Optionally accepts parameters
 * for setting an expiry date and/or token limits for temporary users.
 **/
export function createUser(createOptions?: { token?: string; type?: User["type"] }) {
  return getDatabase()
    .insert(schema.users)
    .values({
      token: createOptions?.token ?? uuid(),
      type: createOptions?.type ?? "normal",
      maxIps: config.maxIpsPerUser,
    })
    .returning();
}

export function isUserExist(token: string) {
  return getDatabase()
    .$count(schema.users, eq(schema.users.token, token))
    .then((v) => v > 0);
}

/** Returns the user with the given token if they exist. */
export function getUser(token: string) {
  return getDatabase().query.users.findFirst({
    where: (fields, { eq }) => eq(fields.token, token),
    with: { usages: true, ips: true },
  });
}

/** Returns a list of all users. */
export function getUsers() {
  return getDatabase().query.users.findMany({
    with: { usages: true, ips: true },
  });
}

export async function getUserPublicData() {
  const users = await getDatabase().query.users.findMany({
    columns: { token: true, nickname: true, type: true, createdAt: true, meta: true },
  });

  return users.map((user) => ({ ...user, meta: user.meta?.public }));
}

export async function getUserUsages() {
  const usages = await getDatabase().query.users_usage.findMany({
    columns: { prompts: true, inputTokens: true, outputTokens: true, modelFamily: true },
  });

  const totalPrompts = usages.reduce((s, curr) => s + curr.prompts, 0);

  const sums = usages.reduce(
    (s, curr) => {
      const cost = getTokenCostUsd(curr);

      s.inputTokens += curr.inputTokens;
      s.outputTokens += curr.outputTokens;

      s.inputCost += cost.input;
      s.outputCost += cost.output;

      return s;
    },
    { inputTokens: 0, outputTokens: 0, inputCost: 0, outputCost: 0 }
  );

  return { totalPrompts, ...sums };
}

/**
 * Rotates the user token by generating a new token and updating the user store.
 * If the token does not exist in the user store, no action is taken.
 * @param token - The current user token.
 * @returns The new user token if the rotation was successful, otherwise undefined.
 */
export async function rotateUserToken(token: string) {
  const db = getDatabase();

  const user = await db.query.users.findFirst({
    where: (fields, { eq }) => eq(fields.token, token),
    columns: { token: true },
  });

  if (!user) return;
  const newToken = uuid();

  await db.transaction(async (tx) => {
    await tx.update(schema.users).set({ token: newToken }).where(eq(schema.users.token, token));
    await tx
      .update(schema.users_ip)
      .set({ userToken: newToken })
      .where(eq(schema.users_ip.userToken, token));
    await tx
      .update(schema.users_usage)
      .set({ userToken: newToken })
      .where(eq(schema.users_usage.userToken, token));
    await tx
      .update(schema.users_ip_usage)
      .set({ userToken: newToken })
      .where(eq(schema.users_ip_usage.userToken, token));
    await tx
      .update(schema.users_events)
      .set({ userToken: newToken })
      .where(eq(schema.users_events.userToken, token));
  });

  return newToken;
}

export async function decrementUserLimit(
  tx: DB,
  token: string,
  data: { model: string; service: LLMService; requested: number }
) {
  const updateFields = {
    promptsUsed: sql`${schema.users_limits.promptsUsed} + 1`,
    tokensUsed: sql`${schema.users_limits.tokensUsed} + ${data.requested}`,
  };

  const updated = await tx
    .update(schema.users_limits)
    .set(updateFields)
    .where(
      and(
        eq(schema.users_limits.userToken, token),
        eq(schema.users_limits.modelFamily, getModelFamilyForService(data.model, data.service))
      )
    )
    .returning({ one: sql`1` });

  if (updated.length === 0) {
    await tx
      .update(schema.users_limits)
      .set(updateFields)
      .where(
        and(eq(schema.users_limits.userToken, token), isNull(schema.users_limits.modelFamily))
      );
  }
}

type UserUsageUpdate = {
  ip: string;
  token: string;
  model: string;
  service: LLMService;

  inputTokens: number;
  outputTokens: number;
};
/** Increments the prompt count for the given user. */
export async function incrementUserUsages(tx: DB, update: UserUsageUpdate) {
  const hashedIp = hashIp(update.ip);
  const modelFamily = getModelFamilyForService(update.model, update.service);

  const updatedFields = (usage: {
    prompts: SQLiteColumn;
    promptsSinceStart: SQLiteColumn;

    inputTokens: SQLiteColumn;
    outputTokens: SQLiteColumn;

    inputTokensSinceStart: SQLiteColumn;
    outputTokensSinceStart: SQLiteColumn;
  }) =>
    ({
      prompts: sql`${usage.prompts} + 1`,
      promptsSinceStart: sql`${usage.promptsSinceStart} + 1`,

      inputTokens: sql`${usage.inputTokens} + ${update.inputTokens}`,
      outputTokens: sql`${usage.outputTokens} + ${update.outputTokens}`,

      inputTokensSinceStart: sql`${usage.inputTokensSinceStart} + ${update.inputTokens}`,
      outputTokensSinceStart: sql`${usage.outputTokensSinceStart} + ${update.outputTokens}`,
    }) as const;

  const data = {
    prompts: 1,
    promptsSinceStart: 1,

    inputTokens: update.inputTokens,
    outputTokens: update.outputTokens,

    inputTokensSinceStart: update.inputTokens,
    outputTokensSinceStart: update.outputTokens,
  };

  await tx
    .insert(schema.users_usage)
    .values({
      modelFamily,
      modelId: update.model,
      userToken: update.token,
      ...data,
    })
    .onConflictDoUpdate({
      target: [
        schema.users_usage.userToken,
        schema.users_usage.modelId,
        schema.users_usage.modelFamily,
      ],
      set: updatedFields(schema.users_usage),
    });

  await tx
    .insert(schema.users_ip_usage)
    .values({
      modelFamily,
      ip: hashedIp,
      modelId: update.model,
      userToken: update.token,
      ...data,
    })
    .onConflictDoUpdate({
      target: [
        schema.users_ip_usage.ip,
        schema.users_ip_usage.userToken,
        schema.users_ip_usage.modelId,
        schema.users_ip_usage.modelFamily,
      ],
      set: updatedFields(schema.users_ip_usage),
    });
}

type SuccessfulAuthentication = { type: "success"; user: User };
type LimitedAuthentication = { type: "limited"; maxIps: number };
type NotFoundAuthentication = { type: "not_found" };
type DisabledAuthentication = {
  type: "disabled";
  disabledUser: { token: string; disabledReason: string | null };
};

export type AuthenticationResult =
  | SuccessfulAuthentication
  | NotFoundAuthentication
  | LimitedAuthentication
  | DisabledAuthentication;

/**
 * Given a user's token and IP address, authenticates the user and adds the IP
 * to the user's list of IPs. Returns the user if they exist and are not
 * disabled, otherwise returns undefined.
 */
export async function authenticate(token: string, ip: string): Promise<AuthenticationResult> {
  const db = getDatabase();

  const user = await db.query.users.findFirst({
    with: { ips: { columns: { ip: true } } },
    where: (fields, { eq }) => eq(fields.token, token),
  });

  if (!user) return { type: "not_found" };
  if (user.isDisabled) return { type: "disabled", disabledUser: user };

  const hashedIp = hashIp(ip);
  const newIp = !user.ips.find(({ ip }) => ip === hashedIp);
  const userLimit = user.maxIps ?? config.maxIpsPerUser;
  const enforcedLimit = user.type === "special" || !userLimit ? Infinity : userLimit;

  if (newIp && user.ips.length >= enforcedLimit) {
    if (config.maxIpsAutoBan) {
      const disabledUser = await db.transaction(async (tx) => {
        await tx.insert(schema.users_ip).values({ userToken: token, ip: hashedIp });

        return await tx
          .update(schema.users)
          .set({ isDisabled: true, disabledReason: "IP address limit exceeded." })
          .where(eq(schema.users.token, token))
          .returning({ token: schema.users.token, disabledReason: schema.users.disabledReason });
      });

      return { disabledUser: disabledUser[0], type: "disabled" };
    }
    return { type: "limited", maxIps: userLimit };
  }

  void db.transaction(async (tx) => {
    if (newIp) {
      await eventLogger.logEvent({
        tx,
        type: "new-ip",
        userToken: token,
        payload: { newIp: hashedIp },
      });

      await tx.insert(schema.users_ip).values({ userToken: token, ip: hashedIp });
    }

    await tx
      .update(schema.users)
      .set({ lastUsedAt: new Date(Date.now()) })
      .where(eq(schema.users.token, token));
  });

  return { type: "success", user };
}

/**
 * Returns true if the user has access to the given model.
 *
 * @param token - The user's token.
 * @param model - The model to check.
 * @param api - The API to check.
 *
 * @returns True if the user has access to the model.
 */
export async function isUserHasAccess(token: string, model: string) {
  const userLimit = await getDatabase().query.users.findFirst({
    where: (fields, { eq }) => eq(fields.token, token),
    columns: { disAllowedModels: true },
  });

  if (!userLimit) throw Error("User not found");
  const limit = superjson.parse<(string | RegExp)[]>(userLimit.disAllowedModels);

  for (const pattern of limit) {
    if (typeof pattern === "string") {
      if (model.includes(pattern)) return false;
    }
    if (pattern instanceof RegExp) {
      if (pattern.test(model)) return false;
    }
  }

  return true;
}

type SuccessfulQuotaCheck = { type: "success" };
type LimitedTokensCheck = { type: "tokens"; used: number; quota: number };
type LimitPromptsCheck = { type: "prompts"; used: number; quota: number };

type QuotaCheckResult = SuccessfulQuotaCheck | LimitedTokensCheck | LimitPromptsCheck;

/**
 * Checks whether the user has available quota for the given model.
 *
 * @param userToken - The user's token.
 * @param data - The model and requested tokens.
 *
 * @returns The result of the quota check.
 */
export async function hasAvailableQuota(
  userToken: string,
  data: { model: string; service: LLMService; requested: number }
): Promise<QuotaCheckResult> {
  const userLimits = await getDatabase().query.users_limits.findFirst({
    orderBy: (fields, { sql }) => sql`(${fields.modelFamily} IS NULL) ASC`,
    where: (fields, { eq, and, or, isNull }) =>
      and(
        eq(fields.userToken, userToken),
        or(
          eq(fields.modelFamily, getModelFamilyForService(data.model, data.service)),
          isNull(fields.modelFamily)
        )
      ),
  });

  if (!userLimits) return { type: "success" };

  const hasAvailablePrompt = userLimits.promptLimits - userLimits.promptsUsed >= 1;
  const hasAvailableToken = userLimits.tokenLimits - userLimits.tokensUsed >= data.requested;

  if (!hasAvailablePrompt && userLimits.promptLimits !== 0) {
    return { type: "prompts", used: userLimits.promptsUsed, quota: userLimits.promptLimits };
  }

  if (!hasAvailableToken && userLimits.tokenLimits !== 0) {
    return { type: "tokens", used: userLimits.tokensUsed, quota: userLimits.tokenLimits };
  }

  return { type: "success" };
}

export async function resetUsage(token: string) {
  const user = await getDatabase().query.users.findFirst({
    where: (fields, { eq }) => eq(fields.token, token),
    columns: { token: true },
  });

  if (!user) return;

  await getDatabase().transaction(async (tx) => {
    await Promise.allSettled([
      tx.delete(schema.users_usage).where(eq(schema.users_usage.userToken, token)),
      tx.delete(schema.users_ip_usage).where(eq(schema.users_ip_usage.userToken, token)),
    ]);
  });
}

/** Disables the given user, optionally providing a reason. */
export async function disableUser(token: string, reason?: string) {
  const db = getDatabase();

  const user = await db.query.users.findFirst({
    where: (fields, { eq }) => eq(fields.token, token),
    columns: { token: true },
  });

  if (!user) return;

  await db
    .update(schema.users)
    .set({ isDisabled: true, disabledAt: new Date(Date.now()), disabledReason: reason })
    .where(eq(schema.users.token, token));
}

export async function deleteUser(token: string) {
  const db = getDatabase();

  const user = await db.query.users.findFirst({
    where: (fields, { eq }) => eq(fields.token, token),
    columns: { token: true, isDisabled: true },
  });

  if (!user) return;
  if (!user.isDisabled) throw Error("User must be disabled before deletion.");

  await db.delete(schema.users).where(eq(schema.users.token, token));
}

async function cleanUpExpiredUsers() {
  const now = new Date(Date.now());
  const threeDaysAgo = new Date(now.getTime() - 72 * 60 * 60 * 1000);

  const db = getDatabase();

  const users = await db.query.users.findMany({
    columns: { token: true },
    where: (fields, { eq, lt }) =>
      eq(fields.type, "temporary") && lt(fields.disabledAt, threeDaysAgo),
  });

  if (users.length === 0) {
    log.info("No expired users found.");
    return;
  }

  log.info({ users: users.length }, "Deleting expired users...");
  await db.delete(schema.users).where(
    inArray(
      schema.users.token,
      users.map((u) => u.token)
    )
  );
}

async function resetUserQuotas() {
  const now = new Date(Date.now());
  const db = getDatabase();

  const users = await db.query.users_limits.findMany({
    where: (fields, { eq, not, and, lt }) =>
      and(not(eq(fields.limitReset, "none")), lt(fields.limitNextReset, now)),
  });

  if (users.length === 0) {
    log.info("No users found with quotas to reset.");
    return;
  }

  log.info({ users: users.length }, "Resetting user quotas...");
  await db.transaction(async (tx) => {
    for (const user of users) {
      let nextReset: Date | null = null;

      switch (user.limitReset) {
        case "hourly":
          nextReset = new Date(now.getTime() + 60 * 60 * 1000);
          break;
        case "daily":
          nextReset = new Date(now.getTime() + 24 * 60 * 60 * 1000);
          break;
        case "weekly":
          nextReset = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
          break;
        case "monthly":
          nextReset = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
          break;
      }

      await tx
        .update(schema.users_limits)
        .set({ promptsUsed: 0, tokensUsed: 0, limitNextReset: nextReset })
        .where(eq(schema.users_limits.userToken, user.userToken));
    }
  });
}

async function initUserDatabase() {
  log.info("Connecting to Database...");

  await getDatabase().transaction(async (tx) => {
    const data = { promptsSinceStart: 0, inputTokensSinceStart: 0, outputTokensSinceStart: 0 };

    await Promise.allSettled([
      tx.update(schema.users_ip_usage).set(data),
      tx.update(schema.users_usage).set(data),
    ]);
  });

  const userCount = await getDatabase().$count(schema.users);
  log.info({ users: userCount }, "Loaded users from Database");
}

export function hashIp(ip: string) {
  // Return if the IP is already hashed or hashing is disabled.
  if (ip.startsWith("ip") || !config.hashIp) return ip;
  return `ip-${crypto.createHash("sha256").update(ip).digest("hex")}`;
}
