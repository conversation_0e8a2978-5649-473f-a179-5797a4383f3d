import { AxiosError, type AxiosResponse } from "axios";

import { type OpenAIModelFamily, getOpenAIModelFamily, isAllowedModel } from "../../models";
import { getAxiosInstance } from "../../network";
import { KeyCheckerBase } from "../key-checker-base";

import type { OpenAIKey, OpenAIKeyProvider } from "./provider";

const axios = getAxiosInstance();

const MIN_CHECK_INTERVAL = 3 * 1000; // 3 seconds
const KEY_CHECK_PERIOD = 60 * 60 * 1000; // 1 hour
const POST_CHAT_COMPLETIONS_URL = "https://api.openai.com/v1/chat/completions";
const GET_MODELS_URL = "https://api.openai.com/v1/models";
const GET_ORGANIZATIONS_URL = "https://api.openai.com/v1/me";

type GetModelsResponse = {
  data: [{ id: string }];
};

type GetOrganizationsResponse = {
  orgs: { data: [{ id: string; is_default: boolean }] };
};

type OpenAIError = {
  error: { type: string; code: string; param: unknown; message: string };
};

type CloneFn = typeof OpenAIKeyProvider.prototype.clone;
type UpdateFn = typeof OpenAIKeyProvider.prototype.update;
type DisableFn = typeof OpenAIKeyProvider.prototype.disable;

export class OpenAIKeyChecker extends KeyCheckerBase<OpenAIKey> {
  private readonly cloneKey: CloneFn;

  constructor(keys: OpenAIKey[], cloneFn: CloneFn, updateKey: UpdateFn, disableKey: DisableFn) {
    super(keys, {
      service: "openai",
      keyCheckPeriod: KEY_CHECK_PERIOD,
      minCheckInterval: MIN_CHECK_INTERVAL,
      recurringChecksEnabled: false,
      updateKey,
      disableKey,
    });
    this.cloneKey = cloneFn;
  }

  protected async testKeyOrFail(key: OpenAIKey) {
    // We only need to check for provisioned models on the initial check.
    const isInitialCheck = !key.firstCheckedAt;

    if (isInitialCheck) {
      const [provisionedModels, livenessTest, verificationTest] = await Promise.all([
        this.getProvisionedModels(key),
        this.testLiveness(key),
        this.testVerification(key),
        this.maybeCreateOrganizationClones(key),
      ]);

      this.updateKey(key.hash, {
        tier: livenessTest.tier,
        modelFamilies: provisionedModels,
        isVerified: verificationTest.isVerified,
        firstCheckedAt: Date.now(),
      });
    } else {
      // No updates needed as models and trial status generally don't change.
      const [_livenessTest] = await Promise.all([this.testLiveness(key)]);
      this.updateKey(key.hash, {});
    }
    this.log.info(
      {
        key: key.hash,
        models: key.modelFamilies,
        tier: key.tier,
        snapshots: key.modelIds,
      },
      "Checked key."
    );
  }

  private async testVerification(key: OpenAIKey) {
    const payload = {
      model: "o3",
      stream: true,
      max_completion_tokens: 1,
      messages: [{ role: "user", content: "" }],
    };

    try {
      await axios.post<OpenAIError>(POST_CHAT_COMPLETIONS_URL, payload, {
        headers: OpenAIKeyChecker.getHeaders(key),
        validateStatus: (status) => status === 200,
      });

      return { isVerified: true };
    } catch (error) {
      return { isVerified: false };
    }
  }

  private async getProvisionedModels(key: OpenAIKey): Promise<OpenAIModelFamily[]> {
    const opts = { headers: OpenAIKeyChecker.getHeaders(key) };
    const { data } = await axios.get<GetModelsResponse>(GET_MODELS_URL, opts);
    const ids = new Set<string>();
    const families = new Set<OpenAIModelFamily>();

    data.data.forEach(({ id }) => {
      ids.add(id);
      families.add(getOpenAIModelFamily(id, "turbo"));
    });

    for (const family of families) {
      if (!isAllowedModel(family)) families.delete(family);
    }

    this.updateKey(key.hash, {
      modelIds: Array.from(ids),
      modelFamilies: Array.from(families),
    });

    return key.modelFamilies;
  }

  private async maybeCreateOrganizationClones(key: OpenAIKey) {
    if (key.organizationId) return; // already cloned
    try {
      const opts = { headers: { Authorization: `Bearer ${key.key}` } };
      const { data } = await axios.get<GetOrganizationsResponse>(GET_ORGANIZATIONS_URL, opts);
      const organizations = data.orgs.data;
      const defaultOrg = organizations.find(({ is_default }) => is_default);
      this.updateKey(key.hash, { organizationId: defaultOrg?.id });
      if (organizations.length <= 1) return;

      this.log.info(
        { parent: key.hash, organizations: organizations.map((org) => org.id) },
        "Key is associated with multiple organizations; cloning key for each organization."
      );

      const ids = organizations.filter(({ is_default }) => !is_default).map(({ id }) => id);
      this.cloneKey(key.hash, ids);
    } catch (error) {
      // Some keys do not have permission to list organizations, which is the
      // typical cause of this error.
      let info: string | Record<string, any>;
      const response = error.response;
      const expectedErrorCodes = ["invalid_api_key", "no_organization"];
      if (expectedErrorCodes.includes(response?.data?.error?.code)) {
        return;
      } else if (response) {
        info = { status: response.status, data: response.data };
      } else {
        info = error.message;
      }

      this.log.warn({ parent: key.hash, error: info }, "Failed to fetch organizations for key.");
      return;
    }

    // It's possible that the keychecker may be stopped if all non-cloned keys
    // happened to be unusable, in which case this clnoe will never be checked
    // unless we restart the keychecker.
    if (!this.timeout) {
      this.log.warn({ parent: key.hash }, "Restarting key checker to check cloned keys.");
      this.scheduleNextCheck();
    }
  }

  protected handleAxiosError(key: OpenAIKey, error: AxiosError) {
    if (error.response && OpenAIKeyChecker.errorIsOpenAIError(error)) {
      const { status, data } = error.response;
      if (status === 401) {
        this.log.warn({ key: key.hash, error: data }, "Key is invalid or revoked. Disabling key.");
        this.disableKey(key, "revoked", "key-checker");
        this.updateKey(key.hash, {
          modelFamilies: ["turbo"],
        });
      } else if (status === 429) {
        switch (data.error.type) {
          case "insufficient_quota":
          case "billing_not_active":
          case "access_terminated":
            const isRevoked = data.error.type === "access_terminated";
            const isOverQuota = !isRevoked;
            const modelFamilies: OpenAIModelFamily[] = isRevoked ? ["turbo"] : key.modelFamilies;
            this.log.warn(
              { key: key.hash, rateLimitType: data.error.type, error: data },
              "Key returned a non-transient 429 error. Disabling key."
            );
            const reason = isRevoked ? "revoked" : "quota";
            this.disableKey(key, reason, "key-checker");
            this.updateKey(key.hash, {
              isOverQuota,
              modelFamilies,
            });
            break;
          case "requests":
            this.log.warn(
              { key: key.hash, error: data },
              "Key is rate limited on text completion endpoint. This is unusual and may indicate a bug. Assuming key is operational."
            );
            this.updateKey(key.hash, { lastCheckedAt: Date.now() });
            break;

          case "tokens":
            // Hitting a token rate limit, even on a trial key, actually implies
            // that the key is valid and can generate completions, so we will
            // treat this as effectively a successful `testLiveness` call.
            this.log.info(
              { key: key.hash },
              "Key is currently `tokens` rate limited; assuming it is operational."
            );
            this.updateKey(key.hash, { lastCheckedAt: Date.now() });
            break;
          default:
            this.log.error(
              { key: key.hash, rateLimitType: data.error.type, error: data },
              "Encountered unexpected rate limit error class while checking key. This may indicate a change in the API; please report this."
            );
            // We don't know what this error means, so we just let the key
            // through and maybe it will fail when someone tries to use it.
            this.updateKey(key.hash, { lastCheckedAt: Date.now() });
        }
      } else {
        this.log.error(
          { key: key.hash, status, error: data },
          "Encountered unexpected error status while checking key. This may indicate a change in the API; please report this."
        );
        this.updateKey(key.hash, { lastCheckedAt: Date.now() });
      }
      return;
    }
    this.log.error(
      { key: key.hash, error: error.message },
      "Network error while checking key; trying this key again in a minute."
    );
    const oneMinute = 60 * 1000;
    const next = Date.now() - (KEY_CHECK_PERIOD - oneMinute);
    this.updateKey(key.hash, { lastCheckedAt: next });
  }

  /**
   * Tests whether the key is valid and has quota remaining. The request we send
   * is actually not valid, but keys which are revoked or out of quota will fail
   * with a 401 or 429 error instead of the expected 400 Bad Request error.
   * This lets us avoid test keys without spending any quota.
   *
   * We use the rate limit header to determine whether it's a trial key.
   */
  private async testLiveness(key: OpenAIKey): Promise<{ tier: OpenAIKey["tier"] }> {
    // What the hell this is doing:

    // OpenAI enforces separate rate limits for chat and text completions. Trial
    // keys have extremely low rate limits of 200 per day per API type. In order
    // to avoid wasting more valuable chat quota, we send an (invalid) chat
    // request to Babbage (a text completion model). Even though our request is
    // to the chat endpoint, we get text rate limit headers back because the
    // requested model determines the rate limit used, not the endpoint.

    // Once we have headers, we can determine:
    // 1. Is the key revoked? (401, OAI doesn't even validate the request)
    // 2. Is the key out of quota? (400, OAI will still validate the request)
    // 3. Is the key a trial key? (400, x-ratelimit-limit-requests: 200)

    // This might still cause issues if too many proxies are running a train on
    // the same trial key and even the text completion quota is exhausted, but
    // it should work better than the alternative.

    const payload = {
      model: "gpt-4o",
      max_completion_tokens: -1,
      messages: [{ role: "user", content: "" }],
    };
    const { headers, data } = await axios.post<OpenAIError>(POST_CHAT_COMPLETIONS_URL, payload, {
      headers: OpenAIKeyChecker.getHeaders(key),
      validateStatus: (status) => status === 400,
    });
    const tier = OpenAIKeyChecker.detectTier(headers);

    // invalid_request_error is the expected error
    if (data.error.type !== "invalid_request_error") {
      this.log.warn(
        { key: key.hash, error: data },
        "Unexpected 400 error class while checking key; assuming key is valid, but this may indicate a change in the API."
      );
    }
    return { tier };
  }

  static errorIsOpenAIError(error: AxiosError): error is AxiosError<OpenAIError> {
    const data = error.response?.data as any;
    return data?.error?.type;
  }

  static getHeaders(key: OpenAIKey) {
    const useOrg = !key.key.includes("svcacct");
    return {
      Authorization: `Bearer ${key.key}`,
      ...(useOrg && key.organizationId && { "OpenAI-Organization": key.organizationId }),
    };
  }

  static detectTier(headers: AxiosResponse["headers"]): OpenAIKey["tier"] {
    const tokensLimit = headers["x-ratelimit-limit-tokens"];
    const intTokensLimit = parseInt(tokensLimit, 10);

    if (!tokensLimit || isNaN(intTokensLimit)) return "unknown";
    if (intTokensLimit <= 30_000) return 1;
    if (intTokensLimit <= 450_000) return 2;
    if (intTokensLimit <= 800_000) return 3;
    if (intTokensLimit <= 2_000_000) return 4;
    if (intTokensLimit <= 30_000_000) return 5;
    return "unknown";
  }
}
