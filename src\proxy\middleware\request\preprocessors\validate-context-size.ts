import type { Request } from "express";
import { z } from "zod/v4";

import { config } from "@/config";

import type { RequestPreprocessor } from "../index";
import { getModelMaxContextLimit } from "@/shared/models";

/**
 * Assigns `req.promptTokens` and `req.outputTokens` based on the request body
 * and outbound API format, which combined determine the size of the context.
 * If the context is too large, an error is thrown.
 * This preprocessor should run after any preprocessor that transforms the
 * request body.
 */
export const validateContextSize: RequestPreprocessor = async (req) => {
  assertRequestHasTokenCounts(req);
  const promptTokens = req.promptTokens;
  const outputTokens = req.outputTokens;
  const contextTokens = promptTokens + outputTokens;
  const model = req.body.model as string;

  let proxyMax: number =
    config.modelFamilySettings.get(req.modelFamily!)?.maxContext ?? config.defaultGlobalMaxContext;

  if (proxyMax === 0) proxyMax = Number.MAX_SAFE_INTEGER;

  if (req.user?.type === "special") {
    req.log.debug("Special user, not enforcing proxy context limit.");
    proxyMax = Number.MAX_SAFE_INTEGER;
  }

  let modelMax = getModelMaxContextLimit(model);
  if (modelMax === null) {
    req.log.warn({ model, service: req.service }, "Unknown model, using 200k token limit");
    modelMax = 200_000;
  }

  const finalMax = Math.min(proxyMax, modelMax);

  z.number()
    .int()
    .max(finalMax, {
      message: `Your request exceeds the context size limit. (max: ${finalMax} tokens, requested: ${promptTokens} prompt + ${outputTokens} output = ${contextTokens} context tokens)`,
    })
    .parse(contextTokens);

  req.log.debug(
    { promptTokens, outputTokens, contextTokens, modelMax, proxyMax },
    "Prompt size validated"
  );

  req.tokenizerInfo.prompt_tokens = promptTokens;
  req.tokenizerInfo.completion_tokens = outputTokens;
  req.tokenizerInfo.max_model_tokens = modelMax;
  req.tokenizerInfo.max_proxy_tokens = proxyMax;
};

function assertRequestHasTokenCounts(
  req: Request
): asserts req is Request & { promptTokens: number; outputTokens: number } {
  z.object({
    promptTokens: z.number().int().min(1),
    outputTokens: z.number().int().min(1),
  })
    .passthrough()
    .parse({ promptTokens: req.promptTokens, outputTokens: req.outputTokens });
}
