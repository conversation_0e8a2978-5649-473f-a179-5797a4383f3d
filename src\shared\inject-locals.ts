import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { config } from "@/config";

import { isAllowedModel, LLM_SERVICES, MODEL_FAMILIES } from "./models";
import { getTokenCostUsd } from "./stats";
import { numberFormat, redactIp } from "./utils";

export const injectLocals: RequestHandler = (req, res, next) => {
  // config-related locals
  res.locals.persistenceEnabled = config.gatekeeperStore !== "memory";
  res.locals.usersEnabled = config.gatekeeper === "user_token";
  res.locals.services = LLM_SERVICES;
  res.locals.modelFamilies = MODEL_FAMILIES;

  res.locals.showTokenCosts = config.showTokenCosts;
  res.locals.maxIps = config.maxIpsPerUser;
  res.locals.ipHashed = config.hashIp;
  res.locals.proxyBasePath = config.proxyBasePath;

  // flash messages
  if (req.session.flash) {
    res.locals.flash = req.session.flash;
    delete req.session.flash;
  } else {
    res.locals.flash = null;
  }

  // view helpers
  res.locals.redactIp = redactIp;
  res.locals.getTokenCostUsd = getTokenCostUsd;
  res.locals.numberFormat = numberFormat.format;
  res.locals.isAllowedModel = isAllowedModel;

  next();
};
