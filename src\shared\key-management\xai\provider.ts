import crypto from "crypto";

import { config } from "@/config";
import { logger } from "@/logger";

import { PaymentRequiredError } from "@/shared/errors";
import { getXAIModelFamily, type XAIModelFamily } from "@/shared/models";
import { getTokenCostUsd } from "@/shared/stats";

import {
  createGenericGetLockoutPeriod,
  defaultUsages,
  type Key,
  type KeyProvider,
  type Usages,
} from "..";
import { prioritizeKeys } from "../prioritize-keys";
import { XAIKeyChecker } from "./checker";

type XAIKeyUsage = {
  [K in XAIModelFamily as `${K}-usages`]: Usages;
};

export interface XAIKey extends Key, XAIKeyUsage {
  readonly service: "xai";
  readonly modelFamilies: XAIModelFamily[];

  /** Set when key check returns a non-transient 429. */
  isOverQuota: boolean;
  /**
   * Model snapshots available.
   */
  modelIds: string[];
}

/**
 * Upon being rate limited, a key will be locked out for this many milliseconds
 * while we wait for other concurrent requests to finish.
 */
const RATE_LIMIT_LOCKOUT = 2000;
/**
 * Upon assigning a key, we will wait this many milliseconds before allowing it
 * to be used again. This is to prevent the queue from flooding a key with too
 * many requests while we wait to learn whether previous ones succeeded.
 */
const KEY_REUSE_DELAY = 500;

export class XAIKeyProvider implements KeyProvider<XAIKey> {
  readonly service = "xai";
  private keys: XAIKey[] = [];
  private checker?: XAIKeyChecker;
  private log = logger.child({ module: "key-provider", service: this.service });

  constructor() {
    const keyConfig = config.keys.xai;

    if (keyConfig.length === 0) {
      this.log.warn("XAI_KEYS is not set. XAI API will not be available.");
      return;
    }

    for (const key of keyConfig) this.addKey({ key, isStartup: true });
    this.log.info({ keyCount: this.keys.length }, "Loaded XAI keys.");
  }

  public addKey({ key, isStartup = false }: { key: string; isStartup?: boolean }): boolean {
    const hash = `xai-${crypto.createHash("sha256").update(key).digest("hex").slice(0, 8)}`;

    if (!isStartup) {
      const isExist = this.keys.find((k) => k.hash === hash);
      if (isExist) return false;
    }

    const newKey: XAIKey = {
      key,
      hash: hash,
      service: this.service,
      modelFamilies: [
        "grok-2",
        "grok-3",
        "grok-3-mini",
        "grok-3-fast",
        "grok-3-mini-fast",
        "grok-4",
      ],
      isDisabled: false,
      isOverQuota: false,
      isRevoked: false,
      disabledReason: undefined,
      disabledAt: undefined,
      disabledBy: undefined,
      input: { tokens: 0, cost: 0 },
      output: { tokens: 0, cost: 0 },
      prompts: 0,
      lastUsedAt: 0,
      rateLimitedAt: 0,
      rateLimitedUntil: 0,
      lastCheckedAt: 0,
      firstCheckedAt: 0,
      addedAt: Date.now(),
      modelIds: [],

      "grok-3-mini-usages": structuredClone(defaultUsages),
      "grok-3-usages": structuredClone(defaultUsages),
      "grok-3-fast-usages": structuredClone(defaultUsages),
      "grok-3-mini-fast-usages": structuredClone(defaultUsages),
      "grok-2-usages": structuredClone(defaultUsages),
      "grok-4-usages": structuredClone(defaultUsages),
    };
    this.keys.push(newKey);
    return true;
  }

  public removeKey(hash: string): boolean {
    const keyIndex = this.keys.findIndex((k) => k.hash === hash);
    if (keyIndex === -1) return false;

    this.keys.splice(keyIndex, 1);
    return true;
  }

  public init() {
    if (config.checkKeys) {
      this.checker = new XAIKeyChecker(this.keys, this.update.bind(this), this.disable.bind(this));
      this.checker.start();
    }
  }

  public list() {
    return this.keys.map((k) => Object.freeze({ ...k, key: undefined }));
  }

  public get(model: string): XAIKey {
    this.log.debug({ model }, "Selecting key");

    const neededFamily = getXAIModelFamily(model);
    const availableKeys = this.keys.filter(
      (k) => !k.isDisabled && !k.isRevoked && k.modelFamilies.includes(neededFamily)
    );

    if (availableKeys.length === 0) {
      throw new PaymentRequiredError(`No XAI keys available for model ${model}`);
    }

    const keysByPriority = prioritizeKeys(availableKeys);

    const selectedKey = keysByPriority[0];
    selectedKey.lastUsedAt = Date.now();
    this.throttle(selectedKey.hash);
    return { ...selectedKey };
  }

  public disable(key: XAIKey, reason?: "quota" | "revoked" | "error", disabledBy?: string) {
    const keyFromPool = this.keys.find((k) => k.hash === key.hash);
    if (!keyFromPool || keyFromPool.isDisabled) return;

    keyFromPool.isDisabled = true;
    keyFromPool.disabledReason = reason;
    keyFromPool.disabledAt = Date.now();
    keyFromPool.disabledBy = disabledBy || "system";

    this.log.warn(
      {
        key: keyFromPool.hash,
        reason: keyFromPool.disabledReason,
        disabledBy: keyFromPool.disabledBy,
      },
      "Key disabled"
    );
  }

  public update(keyHash: string, update: Partial<XAIKey>) {
    const keyFromPool = this.keys.find((k) => k.hash === keyHash)!;
    Object.assign(keyFromPool, { lastCheckedAt: Date.now(), ...update });
  }

  public available() {
    return this.keys.filter((k) => !k.isDisabled).length;
  }

  public incrementUsage(keyHash: string, model: string, inputTokens: number, outputTokens: number) {
    const key = this.keys.find((k) => k.hash === keyHash);
    if (!key) return;

    const modelFamily = getXAIModelFamily(model);
    const cost = getTokenCostUsd({ modelFamily, inputTokens, outputTokens });

    key.prompts++;
    key.input.tokens += inputTokens;
    key.output.tokens += outputTokens;
    key.input.cost += cost.input;
    key.output.cost += cost.output;

    key[`${modelFamily}-usages`].prompts += 1;
    key[`${modelFamily}-usages`].input.tokens += inputTokens;
    key[`${modelFamily}-usages`].output.tokens += outputTokens;
    key[`${modelFamily}-usages`].input.cost += cost.input;
    key[`${modelFamily}-usages`].output.cost += cost.output;
  }

  public getLockoutPeriod = createGenericGetLockoutPeriod(() => this.keys);

  public markRateLimited(keyHash: string) {
    this.log.debug({ key: keyHash }, "Key rate limited");
    const key = this.keys.find((k) => k.hash === keyHash)!;
    const now = Date.now();
    key.rateLimitedAt = now;
    key.rateLimitedUntil = now + RATE_LIMIT_LOCKOUT;
  }

  public recheck() {
    this.keys.forEach(({ hash }) => this.update(hash, { lastCheckedAt: 0, isDisabled: false }));
    this.checker?.scheduleNextCheck();
  }

  private throttle(keyHash: string) {
    const key = this.keys.find((k) => k.hash === keyHash)!;
    const now = Date.now();
    const delay = KEY_REUSE_DELAY;

    key.rateLimitedUntil = Math.max(key.rateLimitedUntil, now + delay);
  }
}
