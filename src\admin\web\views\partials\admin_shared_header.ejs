<meta charset="utf-8" />
<meta name="csrf-token" content="<%= csrfToken %>" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="ie=edge" />

<title><%= title %> - AI Reverse Proxy Admin</title>

<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
<link rel="stylesheet" href="<%= proxyBasePath %>res/css/output.css" />
<script>
  const originalFetch = window.fetch;
  window.fetch = function (input, init) {
    let url = input instanceof Request ? input.url : input;
    if (url.startsWith("/")) {
      const basePath = "<%= proxyBasePath %>";
      // Avoid double slashes if the base path is just "/"
      url = basePath.endsWith("/") ? `${basePath.slice(0, -1)}${url}` : `${basePath}${url}`;
    }
    return originalFetch(url, init);
  };
</script>
