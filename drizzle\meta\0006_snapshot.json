{"version": "6", "dialect": "sqlite", "id": "89a3d498-68d2-47f1-9579-e3046562c5fc", "prevId": "154ba9f1-0d92-4ac5-9352-3c8b374f8579", "tables": {"users-events": {"name": "users-events", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "payload": {"name": "payload", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"idx_user-event": {"name": "idx_user-event", "columns": ["userToken", "type"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"token": {"name": "token", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "nickname": {"name": "nickname", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "adminNote": {"name": "adminNote", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "maxIps": {"name": "maxIps", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'normal'"}, "meta": {"name": "meta", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'{\"private\":{},\"public\":{}}'"}, "disAllowedModels": {"name": "disAllowedModels", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'{\"json\":[]}'"}, "isDisabled": {"name": "isDisabled", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "disabledAt": {"name": "disabledAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "disabledReason": {"name": "disabledReason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "lastUsedAt": {"name": "lastUsedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"idx_user": {"name": "idx_user", "columns": ["token"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users-ip": {"name": "users-ip", "columns": {"userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "lastUsedAt": {"name": "lastUsedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"user_ips_user_ip_unique": {"name": "user_ips_user_ip_unique", "columns": ["userToken", "ip"], "isUnique": true}}, "foreignKeys": {"users-ip_userToken_users_token_fk": {"name": "users-ip_userToken_users_token_fk", "tableFrom": "users-ip", "tableTo": "users", "columnsFrom": ["userToken"], "columnsTo": ["token"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users-ip-usage": {"name": "users-ip-usage", "columns": {"userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelFamily": {"name": "modelFamily", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelId": {"name": "modelId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "inputTokens": {"name": "inputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputTokens": {"name": "outputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "prompts": {"name": "prompts", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "inputTokensSinceStart": {"name": "inputTokensSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputTokensSinceStart": {"name": "outputTokensSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "promptsSinceStart": {"name": "promptsSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "lastUsedAt": {"name": "lastUsedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"user_ip_usage_user_ip_model_id_unique": {"name": "user_ip_usage_user_ip_model_id_unique", "columns": ["ip", "modelId", "userToken", "modelFamily"], "isUnique": true}}, "foreignKeys": {"users-ip-usage_userToken_users_token_fk": {"name": "users-ip-usage_userToken_users_token_fk", "tableFrom": "users-ip-usage", "tableTo": "users", "columnsFrom": ["userToken"], "columnsTo": ["token"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users-limits": {"name": "users-limits", "columns": {"userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "limitNextReset": {"name": "limitNextReset", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "limitReset": {"name": "limitReset", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'none'"}, "modelFamily": {"name": "modelFamily", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "promptLimits": {"name": "promptLimits", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "promptsUsed": {"name": "promptsUsed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "tokenLimits": {"name": "tokenLimits", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "tokensUsed": {"name": "tokensUsed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {}, "foreignKeys": {"users-limits_userToken_users_token_fk": {"name": "users-limits_userToken_users_token_fk", "tableFrom": "users-limits", "tableTo": "users", "columnsFrom": ["userToken"], "columnsTo": ["token"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users-usage": {"name": "users-usage", "columns": {"userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelFamily": {"name": "modelFamily", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelId": {"name": "modelId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "inputTokens": {"name": "inputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputTokens": {"name": "outputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "prompts": {"name": "prompts", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "inputTokensSinceStart": {"name": "inputTokensSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputTokensSinceStart": {"name": "outputTokensSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "promptsSinceStart": {"name": "promptsSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "lastUsedAt": {"name": "lastUsedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"user_usage_user_model_unique": {"name": "user_usage_user_model_unique", "columns": ["userToken", "modelId", "modelFamily"], "isUnique": true}}, "foreignKeys": {"users-usage_userToken_users_token_fk": {"name": "users-usage_userToken_users_token_fk", "tableFrom": "users-usage", "tableTo": "users", "columnsFrom": ["userToken"], "columnsTo": ["token"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"users-usage_userToken_modelId_modelFamily_pk": {"columns": ["userToken", "modelId", "modelFamily"], "name": "users-usage_userToken_modelId_modelFamily_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}