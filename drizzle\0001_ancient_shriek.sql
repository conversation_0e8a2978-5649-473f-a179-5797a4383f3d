ALTER TABLE `user-events` RENAME TO `users-events`;--> statement-breakpoint
CREATE TABLE `users` (
	`token` text PRIMARY KEY NOT NULL,
	`nickname` text,
	`adminNote` text,
	`maxIps` integer NOT NULL,
	`type` text DEFAULT 'normal' NOT NULL,
	`meta` text DEFAULT '{"private":{},"public":{}}',
	`isDisabled` integer,
	`disabledAt` integer,
	`disabledReason` text,
	`createdAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	`lastUsedAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_user` ON `users` (`token`);--> statement-breakpoint
CREATE TABLE `users-ip` (
	`userToken` text NOT NULL,
	`ip` text NOT NULL,
	`createdAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	`lastUsedAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	FOREIGN KEY (`userToken`) REFERENCES `users`(`token`) ON UPDATE cascade ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `user_ips_user_ip_unique` ON `users-ip` (`userToken`,`ip`);--> statement-breakpoint
CREATE TABLE `users-ip-usage` (
	`userToken` text NOT NULL,
	`ip` text NOT NULL,
	`modelFamily` text NOT NULL,
	`modelId` text NOT NULL,
	`prompts` integer DEFAULT 0 NOT NULL,
	`tokens` integer DEFAULT 0 NOT NULL,
	`tokensSinceStart` integer DEFAULT 0 NOT NULL,
	`promptsSinceStart` integer DEFAULT 0 NOT NULL,
	`createdAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	`lastUsedAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	FOREIGN KEY (`userToken`) REFERENCES `users`(`token`) ON UPDATE cascade ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `user_ip_usage_user_ip_model_id_unique` ON `users-ip-usage` (`ip`,`modelId`,`userToken`,`modelFamily`);--> statement-breakpoint
CREATE TABLE `users-usage` (
	`userToken` text NOT NULL,
	`modelFamily` text NOT NULL,
	`modelId` text NOT NULL,
	`tokens` integer DEFAULT 0 NOT NULL,
	`prompts` integer DEFAULT 0 NOT NULL,
	`tokensSinceStart` integer DEFAULT 0 NOT NULL,
	`promptsSinceStart` integer DEFAULT 0 NOT NULL,
	`createdAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	`lastUsedAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	PRIMARY KEY(`userToken`, `modelId`, `modelFamily`),
	FOREIGN KEY (`userToken`) REFERENCES `users`(`token`) ON UPDATE cascade ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `user_usage_user_model_unique` ON `users-usage` (`userToken`,`modelId`,`modelFamily`);