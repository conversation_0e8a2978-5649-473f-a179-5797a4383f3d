import { type Request, type <PERSON><PERSON><PERSON><PERSON><PERSON>, Router } from "express";

import { cacheStore } from "@/shared/cache";
import { type DeepSeek<PERSON>ey, keyPool } from "@/shared/key-management";
import { isAllowedModel } from "@/shared/models";
import { createModelList } from "@/shared/utils";

import { addKey, createPreprocessorMiddleware, finalizeBody } from "../middleware/request";
import { createQueuedProxyMiddleware } from "../middleware/request/proxy-middleware-factory";
import type { ProxyResHandlerWithBody } from "../middleware/response";
import { ipLimiter } from "../rate-limit";

export function getModelsResponse() {
  const provider = keyPool.getKeyProvider("deepseek");
  if (provider.available() === 0) return [];

  const keys = provider.list() as DeepSeekKey[];
  const modelIds = Array.from(new Set(keys.map((k) => k.modelIds).flat()));

  return createModelList(modelIds, "deepseek", (id) => isAllowedModel(id));
}

const handleModelRequest: RequestHandler = async (_req, res) => {
  const cache = await cacheStore.get<ReturnType<typeof getModelsResponse>>("deepseek");

  if (cache) {
    res.setHeader("Cache-State", "HIT");
    return res.status(200).json({ object: "list", data: cache });
  }

  const models = getModelsResponse();
  await cacheStore.set("deepseek", models);

  res.setHeader("Cache-State", "MISS");
  return res.status(200).json({ object: "list", data: models });
};

const deepseekResponseHandler: ProxyResHandlerWithBody = async (_proxyRes, req, res, body) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  let newBody = body;
  res.status(200).json({ ...newBody, proxy: body.proxy });
};

function removeReasonerStuff(req: Request) {
  if (String(req.body.model).includes("reasoner")) {
    // https://api-docs.deepseek.com/guides/reasoning_model
    delete req.body.presence_penalty;
    delete req.body.frequency_penalty;
    delete req.body.temperature;
    delete req.body.top_p;

    delete req.body.logprobs;
    delete req.body.top_logprobs;
  }
}

function fixChatPrefix(req: Request) {
  const body = req.body as {
    model: string;
    messages: { role: "user" | "assistant" | "system"; content: string }[];
  };
  const lastMessage = body.messages[body.messages.length - 1];

  // Only reasoner model force the last message to be the assistant or user.
  if (body.messages.length && body.model.includes("reasoner")) {
    /**
     * If the last message is not an assistant or user message,
     * then we throw error instead try to edit the data.
     */
    if (lastMessage.role !== "assistant" && lastMessage.role !== "user") {
      throw new Error("Last message role must be assistant or user!");
    }

    /**
     * We should only add the prefix to the last message if it's an assistant message.
     * As it it's the client responsibility to ensure the last message follow the correct format.
     */
    if (lastMessage.role === "assistant") {
      req.body.messages[body.messages.length - 1] = { ...lastMessage, prefix: true };
    }
  }
}

const deepseekProxy = createQueuedProxyMiddleware({
  mutations: [addKey, finalizeBody],
  target: "https://api.deepseek.com/beta",
  blockingResponseHandler: deepseekResponseHandler,
});

const deepseekRouter = Router();
deepseekRouter.get("/v1/models", handleModelRequest);

deepseekRouter.post(
  "/v1/chat/completions",
  ipLimiter,
  createPreprocessorMiddleware(
    { inboundApi: "openai", outboundApi: "openai", service: "deepseek" },
    { afterTransform: [fixChatPrefix, removeReasonerStuff] }
  ),
  deepseekProxy
);

// Redirect browser requests to the homepage.
deepseekRouter.get("*", (req, res, next) => {
  const isBrowser = req.headers["user-agent"]?.includes("Mozilla");
  if (isBrowser) res.redirect("/");
  else next();
});

export const deepseek = deepseekRouter;
