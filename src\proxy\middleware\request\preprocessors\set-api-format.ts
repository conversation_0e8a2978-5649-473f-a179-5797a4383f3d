import type { APIFormat } from "@/shared/key-management";
import type { LLMService } from "@/shared/models";
import type { RequestPreprocessor } from "../index";

/**
 * Creates a middleware that configures the API format properties on the request object.
 *
 * This middleware sets the inbound and outbound API formats, which determine:
 * - How the proxy interprets the incoming request body (inboundApi)
 * - What format to use when making the request to the backend service (outboundApi)
 * - Which service to route the request to (service)
 *
 * If inboundApi differs from outboundApi, the proxy will automatically transform
 * the request body and response between the two formats.
 *
 * @param config - Configuration object specifying API formats and service
 * @returns A request preprocessor middleware function
 */
export const setApiFormat = (config: {
  /**
   * The API format of the request as submitted by the client.
   * This determines how the proxy interprets the incoming request body.
   */
  inboundApi: APIFormat;
  /**
   * The API format that will be used when making the request to the backend service.
   * If different from inboundApi, the proxy will transform the request body accordingly.
   */
  outboundApi: APIFormat;
  /**
   * The service the request will be sent to, which determines authentication
   * and possibly the streaming transport.
   */
  service: LLMService;
}): RequestPreprocessor => {
  return function configureRequestApiFormat(req) {
    req.inboundApi = config.inboundApi;
    req.outboundApi = config.outboundApi;
    req.service = config.service;
  };
};
