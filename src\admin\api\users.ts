import { countDistinct, desc, eq, sql } from "drizzle-orm";
import { Router } from "express";
import { z } from "zod/v4";

import { config } from "@/config";
import { getDatabase } from "@/shared/database";
import { schema } from "@/shared/database/database";
import * as userStore from "@/shared/users/user-store";

import type { ModelFamily } from "@/shared/models";
import { getTokenCostUsd } from "@/shared/stats";

const router = Router();

/**
 * Returns a list of all users, sorted by last used time.
 *
 * GET /admin/api/users
 */
router.get("/", async (req, res) => {
  const db = getDatabase();

  const usageSubquery = db.$with("usage_subquery").as(
    db
      .select({
        userToken: schema.users_usage.userToken,
        inputTokens: sql<number>`SUM(${schema.users_usage.inputTokens})`.as("inputTokens"),
        outputTokens: sql<number>`SUM(${schema.users_usage.outputTokens})`.as("outputTokens"),
        prompts: sql<number>`SUM(${schema.users_usage.prompts})`.as("prompts"),
      })
      .from(schema.users_usage)
      .groupBy(schema.users_usage.userToken)
  );

  const ipSubquery = db.$with("ip_subquery").as(
    db
      .select({
        userToken: schema.users_ip.userToken,
        ips: countDistinct(schema.users_ip.ip).as("ips"),
      })
      .from(schema.users_ip)
      .groupBy(schema.users_ip.userToken)
  );

  const data = await db
    .with(usageSubquery, ipSubquery)
    .select({
      token: schema.users.token,
      nickname: schema.users.nickname,
      type: schema.users.type,
      adminNote: schema.users.adminNote,
      createdAt: schema.users.createdAt,
      lastUsedAt: schema.users.lastUsedAt,
      ips: sql<number>`COALESCE(${ipSubquery.ips}, 0)`,
      inputTokens: sql<number>`COALESCE(${usageSubquery.inputTokens}, 0)`,
      outputTokens: sql<number>`COALESCE(${usageSubquery.outputTokens}, 0)`,
      prompts: sql<number>`COALESCE(${usageSubquery.prompts}, 0)`,
      isDisabled: schema.users.isDisabled,
      disabledReason: schema.users.disabledReason,
      disabledAt: schema.users.disabledAt,
      maxIps: schema.users.maxIps,
    })
    .from(schema.users)
    .leftJoin(usageSubquery, eq(usageSubquery.userToken, schema.users.token))
    .leftJoin(ipSubquery, eq(ipSubquery.userToken, schema.users.token))
    .orderBy(desc(schema.users.lastUsedAt));

  return res.json(data);
});

/**
 * Returns the user with the given token.
 *
 * GET /admin/api/users/:token
 */
router.get("/:token", async (req, res) => {
  const inputSchema = z.object({
    token: z.coerce.string(),
    limits: z.coerce.number().optional().default(1000),
  });

  const result = inputSchema.safeParse({ ...req.params, ...req.query });
  if (!result.success) {
    return res.status(400).json({ error: { message: result.error } });
  }

  const db = getDatabase();

  const data = await db.query.users.findFirst({
    where: (fields, { eq }) => eq(fields.token, result.data.token),
    with: { ips: true },
  });

  if (!data) {
    return res.status(404).json({ error: { message: "User not found" } });
  }

  const userUsages = db
    .select({
      modelFamily: schema.users_usage.modelFamily,
      prompts: sql<number>`SUM(${schema.users_usage.prompts})`,
      inputTokens: sql<number>`SUM(${schema.users_usage.inputTokens})`,
      outputTokens: sql<number>`SUM(${schema.users_usage.outputTokens})`,
      promptsSinceStart: sql<number>`SUM(${schema.users_usage.promptsSinceStart})`,
      inputTokensSinceStart: sql<number>`SUM(${schema.users_usage.inputTokensSinceStart})`,
      outputTokensSinceStart: sql<number>`SUM(${schema.users_usage.outputTokensSinceStart})`,
      lastUsedAt: sql<number>`MAX(${schema.users_usage.lastUsedAt})`,
    })
    .from(schema.users_usage)
    .where(eq(schema.users_usage.userToken, data.token))
    .groupBy(schema.users_usage.modelFamily)
    .orderBy(sql`${schema.users_usage.lastUsedAt} DESC`);

  const usages = (await userUsages).map((usage) => {
    const cost = getTokenCostUsd(usage);
    const costSinceStart = getTokenCostUsd({
      modelFamily: usage.modelFamily,
      inputTokens: usage.inputTokensSinceStart,
      outputTokens: usage.outputTokensSinceStart,
    });

    return {
      modelFamily: usage.modelFamily,
      total: {
        input: { tokens: usage.inputTokens, cost: cost.input },
        output: { tokens: usage.outputTokens, cost: cost.output },
        prompts: usage.prompts,
      },
      sinceStart: {
        input: { tokens: usage.inputTokensSinceStart, cost: costSinceStart.input },
        output: { tokens: usage.outputTokensSinceStart, cost: costSinceStart.output },
        prompts: usage.promptsSinceStart,
      },
    };
  });

  return res.json({ user: data, usages });
});

/**
 * Get chat-completions events for the given user token.
 * Returns the list of events.
 *
 * GET /admin/api/users/:token/logs
 */
router.get("/:token/logs", async (req, res) => {
  const token = req.params.token;
  const db = getDatabase();

  if (!config.eventLogging) {
    return res.status(404).json({ error: "Event logging is disabled" });
  }

  if (!token) {
    return res.status(400).json({ error: "No token provided" });
  }

  const data = db.query.users_events.findMany({
    where: (fields, { eq, and }) =>
      and(eq(fields.userToken, token), eq(fields.type, "chat-completion")),
    orderBy: (fields, { desc }) => desc(fields.createdAt),
    columns: { id: false, userToken: false },
    extras: (field, { sql }) => ({
      inputTokens: sql<number>`json_extract(${field.payload}, '$.inputTokens')`.as("inputTokens"),
      outputTokens: sql<number>`json_extract(${field.payload}, '$.outputTokens')`.as(
        "outputTokens"
      ),
      modelFamily: sql<ModelFamily>`json_extract(${field.payload}, '$.family')`.as("modelFamily"),
    }),
  });

  const logs = (await data).map((log) => {
    const cost = getTokenCostUsd(log);

    return {
      modelFamily: log.modelFamily,
      input: { tokens: log.inputTokens, cost: cost.input },
      output: { tokens: log.outputTokens, cost: cost.output },
      createdAt: log.createdAt,
      payload: log.payload,
    };
  });

  return res.json({ logs });
});

/**
 * Rotates the user token for the given user.
 * Returns the new user token.
 *
 * POST /admin/api/users/:token/rotate
 */
router.post("/:token/rotate", async (req, res) => {
  const isUserExist = await userStore.isUserExist(req.params.token);
  if (!isUserExist) {
    return res.status(404).json({ error: { message: "User not found" } });
  }

  const newToken = await userStore.rotateUserToken(req.params.token);
  return res.status(200).json({ newToken });
});

/**
 * Creates a new user.
 * Optionally accepts a JSON body containing `type`, and for temporary-type
 * users, `tokenLimits` and `expiresAt` fields.
 * Returns the created user's token.
 *
 * POST /admin/api/users/create
 */
router.post("/create", async (req, res) => {
  const body = req.body;

  const validateSchema = z.discriminatedUnion("type", [
    z.object({ type: z.literal("normal") }),
    z.object({ type: z.literal("special") }),
    z.object({ type: z.literal("temporary"), promptLimits: z.number().int().min(1) }),
  ]);

  const result = validateSchema.safeParse(body);
  if (!result.success) {
    return res.status(400).json({ error: result.error });
  }

  const db = getDatabase();

  const data = await db.transaction(async (tx) => {
    const token = await db
      .insert(schema.users)
      .values({ type: result.data.type })
      .returning({ token: schema.users.token });

    if (result.data.type === "temporary") {
      await tx
        .insert(schema.users_limits)
        .values({ userToken: token[0].token, promptLimits: result.data.promptLimits });
    }

    return token[0];
  });

  return res.json({ token: data.token });
});

/**
 * Updates the user with the given token, creating them if they don't exist.
 * Accepts a JSON body containing at least one field on the User type.
 * Returns the upserted user.
 *
 * PUT /admin/api/users/:token
 */
router.put("/:token", async (req, res) => {
  const result = z
    .object({
      nickname: z.string().optional(),
      adminNote: z.string().optional(),
      type: z.enum(["normal", "special", "temporary"]).optional(),
      maxIps: z.number().optional(),
    })
    .safeParse({ ...req.body });

  if (!result.success) {
    return res.status(400).json({ error: { message: z.prettifyError(result.error) } });
  }

  await getDatabase().transaction(async (tx) => {
    await tx.update(schema.users).set(result.data).where(eq(schema.users.token, req.params.token));
  });

  res.json({ success: true, data: { ...result.data, token: req.params.token } });
});

/**
 * Disables the user with the given token. Optionally accepts a `disabledReason`
 * Returns the disabled user token.
 *
 * POST /admin/api/users/:token/deactivate
 */
router.post("/:token/deactivate", async (req, res) => {
  const disabledReason = z
    .string()
    .optional()
    .safeParse(req.body.disabledReason || "No reason provided!");

  if (!disabledReason.success) {
    return res.status(400).json({ error: { message: disabledReason.error } });
  }

  const user = await userStore.getUser(req.params.token);
  if (!user) {
    return res.status(404).json({ error: { message: "User not found" } });
  }

  if (user.isDisabled) {
    return res.status(400).json({ error: { message: "User already disabled" } });
  }

  await getDatabase()
    .update(schema.users)
    .set({
      isDisabled: true,
      disabledAt: new Date(Date.now()),
      disabledReason: disabledReason.data,
    })
    .where(eq(schema.users.token, user.token));

  return res.status(200).json({ token: user.token });
});

/**
 * Reactivates the user with the given token.
 * Returns the reactivated user token.
 *
 * POST /admin/api/users/:token/reactivate
 */
router.post("/:token/reactivate", async (req, res) => {
  const user = await userStore.getUser(req.params.token);
  if (!user) {
    return res.status(404).json({ error: { message: "User not found" } });
  }

  if (!user.isDisabled) {
    return res.status(400).json({ error: { message: "User not disabled" } });
  }

  await getDatabase()
    .update(schema.users)
    .set({
      isDisabled: false,
      disabledAt: null,
      disabledReason: null,
    })
    .where(eq(schema.users.token, user.token));

  return res.status(200).json({ token: user.token });
});

/**
 * Deletes the user with the given token.
 * Returns the deleted user token.
 *
 * POST /admin/api/users/:token/delete
 */
router.post("/:token/delete", async (req, res) => {
  const user = await userStore.getUser(req.params.token);
  if (!user) {
    return res.status(404).json({ error: { message: "User not found" } });
  }

  if (!user.isDisabled) {
    return res.status(400).json({ error: { message: "User not disabled" } });
  }

  await getDatabase().delete(schema.users).where(eq(schema.users.token, user.token));

  return res.status(200).json({ token: user.token });
});

export { router as usersApiRouter };
