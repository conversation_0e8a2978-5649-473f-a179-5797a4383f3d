{
  "compilerOptions": {
    "lib": ["ESNext"],
    "target": "ESNext",
    "module": "ESNext",
    "moduleDetection": "force",
    "esModuleInterop": true,
    "allowJs": true,

    // Bundler mode
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "noEmit": true,

    // Best practices
    "strict": true,
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "noFallthroughCasesInSwitch": true,

    // Some stricter flags
    "resolveJsonModule": true,
    "useUnknownInCatchVariables": false,
    "forceConsistentCasingInFileNames": true,

    // Disable some strict flags
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noPropertyAccessFromIndexSignature": false,

    /* Path Aliases */
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "exclude": ["node_modules"],
  "files": ["src/shared/custom.d.ts"]
}
