import { Router } from "express";
import rateLimit from "express-rate-limit";

import { eq } from "drizzle-orm";
import { z } from "zod/v4";

import { config } from "@/config";
import { getDatabase } from "@/shared/database";
import { schema } from "@/shared/database/database";
import { eventLogger } from "@/shared/logging";
import type { ModelFamily } from "@/shared/models";
import { getTokenCostUsd } from "@/shared/stats";
import * as userStore from "@/shared/users/user-store";
import { sanitizeAndTrim } from "@/shared/utils";

import { getUserInfo, truncateToken } from "../web/self-service";

const userLookupLimiter = rateLimit({
  windowMs: 10 * 60 * 1000,
  max: 50,
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    const exemptIps = ["127.0.0.1", "::1"];
    return exemptIps.includes(req.ip);
  },
  handler: (_req, res, _next, options) => {
    res.status(options.statusCode).json({
      error: {
        message: "Too many requests, please try again later.",
        status: options.statusCode,
      },
    });
  },
});

const router = Router();
router.use(userLookupLimiter);

router.post("/lookup", async (req, res) => {
  const token: string = req.body.token ?? "";
  const user = await getUserInfo(token);

  req.log.info({ token: truncateToken(token), success: !!user }, "User self-service api lookup");

  if (!user) {
    return res.status(400).json({ error: "Invalid user token." });
  }

  if (req.headers.referer?.includes("/user/lookup")) {
    req.session.userToken = token;
  }

  void eventLogger.logEvent({
    type: "user-action",
    userToken: user.token,
    payload: {
      ip: userStore.hashIp(req.ip),
      action: "self-service-lookup",
    },
  });

  return res.json(user);
});

router.post("/clear-token", async (req, res) => {
  // Clear the user token from the session
  if (req.session.userToken) {
    delete req.session.userToken;
    req.log.info("User token cleared from session");
  }

  return res.json({ success: true, message: "Token cleared from session" });
});

router.get("/logs", async (req, res) => {
  const token = req.query.token as string;
  const db = getDatabase();

  if (!config.eventLogging) {
    return res.status(404).json({ error: "Event logging is disabled" });
  }

  if (!token) {
    return res.status(400).json({ error: "No token provided" });
  }

  const data = db.query.users_events.findMany({
    where: (fields, { eq, and }) =>
      and(eq(fields.userToken, token), eq(fields.type, "chat-completion")),
    orderBy: (fields, { desc }) => desc(fields.createdAt),
    limit: 500,
    columns: { id: false, userToken: false },
    extras: (field, { sql }) => ({
      inputTokens: sql<number>`json_extract(${field.payload}, '$.inputTokens')`.as("inputTokens"),
      outputTokens: sql<number>`json_extract(${field.payload}, '$.outputTokens')`.as(
        "outputTokens"
      ),
      modelFamily: sql<ModelFamily>`json_extract(${field.payload}, '$.family')`.as("modelFamily"),
    }),
  });

  const logs = (await data).map((log) => {
    const cost = getTokenCostUsd(log);

    return {
      modelFamily: log.modelFamily,
      input: { tokens: log.inputTokens, cost: cost.input },
      output: { tokens: log.outputTokens, cost: cost.output },
      createdAt: log.createdAt,
      payload: log.payload,
    };
  });

  return res.json({ logs });
});

router.post("/edit-nickname", async (req, res) => {
  const token = req.body.token;
  const db = getDatabase();

  const user = await db.query.users.findFirst({
    where: (fields, { eq }) => eq(fields.token, token),
    columns: { token: true, disabledAt: true, nickname: true },
  });

  if (!user) {
    return res.status(400).json({ error: { message: "Invalid user token." } });
  }

  if (!config.allowNicknameChanges || user.disabledAt) {
    return res.status(403).json({ error: { message: "Nickname changes are not allowed." } });
  }

  const isIpAllowed = await db.query.users_ip.findFirst({
    columns: { ip: true },
    where: (fields, { eq }) =>
      eq(fields.userToken, token) && eq(fields.ip, userStore.hashIp(req.ip)),
  });

  if (!isIpAllowed) {
    return res.status(403).json({
      error: {
        ip: req.ip,
        message: "Nickname changes are only allowed from registered IPs.",
      },
    });
  }

  const nicknameValidator = z
    .string()
    .nullish()
    .transform((v) => (v ? sanitizeAndTrim(v) : null));

  const result = nicknameValidator.safeParse(req.body.nickname);
  if (!result.success) {
    return res.status(400).json({ error: result.error.message });
  }

  const data = await db
    .update(schema.users)
    .set({ nickname: result.data })
    .where(eq(schema.users.token, token))
    .returning({ nickname: schema.users.nickname });

  void eventLogger.logEvent({
    type: "user-action",
    userToken: token,
    payload: {
      ip: userStore.hashIp(req.ip),
      action: "self-service-nickname-change",
      payload: { old: user.nickname, new: data[0].nickname },
    },
  });

  return res.json({ message: "Nickname updated.", nickname: data[0].nickname });
});

export { router as selfServiceAPIRouter };
