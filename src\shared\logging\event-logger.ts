import { config } from "@/config";

import type { ExtractTablesWithRelations } from "drizzle-orm";
import type { SQLiteTransaction } from "drizzle-orm/sqlite-core";

import { getDatabase } from "../database";
import * as schema from "../database/schema";
import type { EventLogPayloadData } from "../database/schema/events";

type Schema = typeof schema;

type LogEventPayloadParams<T extends keyof EventLogPayloadData> = {
  tx?: SQLiteTransaction<"sync", void, Schema, ExtractTablesWithRelations<Schema>>;
  type: T;
  userToken?: string;
  payload: EventLogPayloadData[T];
};

export async function logEvent<T extends keyof EventLogPayloadData>({
  tx,
  payload,
  userToken,
  type,
}: LogEventPayloadParams<T>) {
  if (!config.eventLogging) return;

  const db = tx ?? getDatabase();
  await db
    .insert(schema.users_events)
    .values({ type, payload, userToken: userToken ?? "No token" })
    .execute();
}
