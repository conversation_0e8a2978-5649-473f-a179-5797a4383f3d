export class HttpError extends <PERSON>rror {
  constructor(public status: number, message: string) {
    super(message);
    this.name = "HttpError";
  }
}

export class BadRequestError extends HttpError {
  constructor(message: string) {
    super(400, message);
  }
}

export class PaymentRequiredError extends HttpError {
  constructor(message: string) {
    super(402, message);
  }
}

export class ForbiddenError extends HttpError {
  constructor(message: string) {
    super(403, message);
  }
}

export class NotFoundError extends HttpError {
  constructor(message: string) {
    super(404, message);
  }
}

export class TooManyRequestsError extends HttpError {
  constructor(message: string) {
    super(429, message);
  }
}

export class RetryableError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "RetryableError";
  }
}
