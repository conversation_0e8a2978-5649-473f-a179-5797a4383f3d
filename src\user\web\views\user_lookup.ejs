<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="<%= csrfToken %>" />
    <title>User Token Lookup</title>

    <link href="<%= proxyBasePath %>res/css/output.css" rel="stylesheet" />
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
      const originalFetch = window.fetch;
      window.fetch = function (input, init) {
        let url = input instanceof Request ? input.url : input;
        if (url.startsWith("/")) {
          const basePath = "<%= proxyBasePath %>";
          // Avoid double slashes if the base path is just "/"
          url = basePath.endsWith("/") ? `${basePath.slice(0, -1)}${url}` : `${basePath}${url}`;
        }
        return originalFetch(url, init);
      };
    </script>
  </head>

  <body class="bg-background font-mono text-white">
    <div x-data="userLookup" class="mx-auto flex w-full max-w-7xl flex-col gap-4 p-4">
      <h1 class="mt-2 mb-6 text-center text-3xl font-bold">User Token Lookup</h1>

      <!-- Lookup Form -->
      <div class="card" id="lookup-form-card">
        <div class="mb-6 flex flex-col gap-4">
          <div class="flex flex-col gap-2">
            <label for="token" class="text-accent font-bold">Enter your token:</label>
            <input
              id="token"
              x-model="token"
              type="password"
              placeholder="Your token here"
              @keyup.enter="lookupUser()"
              class="border-background-secondary bg-background-tertiary rounded-lg border p-3 text-base text-white"
            />
          </div>
          <div class="flex w-full gap-2">
            <button class="primary-button flex-1" @click="lookupUser()">
              <span>🔍</span> Lookup
            </button>
            <button class="danger-button w-max" @click="reset()"><span>🔄</span> Clear</button>
          </div>
        </div>
      </div>

      <!-- Loading state -->
      <div class="flex flex-col items-center justify-center gap-4 p-8" x-show="loading">
        <div
          class="border-t-accent h-10 w-10 animate-spin rounded-full border-4 border-r-transparent border-b-transparent border-l-transparent"
        ></div>
        <p>Loading user data...</p>
      </div>

      <!-- Error message -->
      <div
        class="my-4 rounded-lg border border-[rgba(244,67,54,0.3)] bg-[rgba(244,67,54,0.1)] p-4 text-[#ff8a80]"
        x-show="error"
        x-text="error"
      ></div>

      <!-- User information -->
      <template x-if="userData && !loading">
        <div class="card" id="user-info-card">
          <div class="mb-6 flex items-center justify-between">
            <h2 class="text-xl font-bold">User Information</h2>

            <div class="flex gap-4">
              <button class="primary-button" @click="openEditModal(userData.nickname || '')">
                <span class="inline-block text-base">✏️</span> Edit Nickname
              </button>
            </div>
          </div>

          <div class="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Token:</span>
              <code class="w-max text-base" x-text="truncateToken(userData.token)"></code>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Nickname:</span>
              <span x-text="userData.nickname || 'None'"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Type:</span>
              <span x-text="userData.type"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Created:</span>
              <span x-text="formatDate(userData.createdAt)"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Last Used:</span>
              <span x-text="formatDate(userData.lastUsedAt) || 'Never'"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">IPs Count:</span>
              <span x-text="userData.ips + ' Registered IPs'"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Status:</span>
              <div>
                <span
                  x-text="userData.isDisabled ? 'Disabled' : 'Active'"
                  :class="userData.isDisabled ? 'text-danger font-bold' : 'text-success font-bold'"
                ></span>
                <span
                  x-show="userData.disabledReason"
                  x-text="` (${formatDate(userData.disabledAt)})`"
                ></span>
              </div>
            </div>

            <div class="flex flex-col gap-2" x-show="userData.isDisabled">
              <span class="text-accent font-bold">Reason:</span>
              <span x-text="userData.disabledReason || 'N/A'"></span>
            </div>
          </div>

          <div class="flex flex-col gap-2" x-show="userData.meta.public">
            <span class="text-accent font-bold">Public Meta:</span>
            <ul class="list-inside list-disc">
              <template x-for="(value, key) in userData.meta.public" :key="key">
                <li><span x-text="key"></span>: <span x-text="value"></span></li>
              </template>
            </ul>
          </div>
        </div>
      </template>

      <!-- Usage Statistics -->
      <div class="card" x-show="userData && !loading && userLogs.length > 0" id="usage-stats-card">
        <div class="mb-6 flex items-center justify-between">
          <h2 class="text-xl font-bold">Usage Statistics</h2>
        </div>

        <div class="mb-3 grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
          <div
            class="bg-background-tertiary border-border flex flex-col gap-2 rounded-lg border p-4 text-center"
          >
            <h3 class="text-accent m-0 text-base">Total Requests</h3>
            <div class="text-xl font-bold" x-text="formatNumber(getTotalPrompts())"></div>
          </div>

          <div
            class="bg-background-tertiary border-border flex flex-col gap-2 rounded-lg border p-4 text-center"
          >
            <h3 class="text-accent m-0 text-base">Last Request</h3>
            <div class="text-xl font-bold" x-text="getLastRequestDate()"></div>
            <div class="text-accent-light text-sm" x-text="getLastRequestModel() || 'N/A'"></div>
          </div>

          <div
            class="bg-background-tertiary border-border flex flex-col gap-2 rounded-lg border p-4 text-center"
          >
            <h3 class="text-accent m-0 text-base">Most Used Model</h3>
            <div class="text-xl font-bold" x-text="getMostUsedModel().model || 'N/A'"></div>
            <div
              class="text-accent-light text-sm"
              x-text="getMostUsedModel().count ? formatNumber(getMostUsedModel().count) + ' requests' : ''"
            ></div>
          </div>
        </div>

        <!-- Token Breakdown -->
        <div
          class="bg-background-tertiary border-border flex flex-wrap items-center justify-center gap-6 rounded-lg border p-4"
        >
          <div class="flex items-center gap-2">
            <span class="text-accent font-bold">Input Tokens:</span>
            <span class="font-bold" x-text="formatNumber(getTotalInputTokens())"></span>
            <span class="text-accent-light text-sm" x-text="formatCost(getInputCost())"></span>
          </div>

          <div class="flex items-center gap-2">
            <span class="text-accent font-bold">Output Tokens:</span>
            <span class="font-bold" x-text="formatNumber(getTotalOutputTokens())"></span>
            <span class="text-accent-light text-sm" x-text="formatCost(getOutputCost())"></span>
          </div>

          <div class="flex items-center gap-2">
            <span class="text-accent font-bold">Total Tokens:</span>
            <span class="font-bold" x-text="formatNumber(getTotalTokens())"></span>
            <span class="text-accent-light text-sm" x-text="formatCost(getTotalCost())"></span>
          </div>
        </div>

        <!-- Usage Chart -->
        <div class="bg-background-tertiary border-border mt-3 h-[400px] rounded-lg border p-4">
          <canvas id="usageChart"></canvas>
        </div>

        <!-- Model Usage Breakdown -->
        <div class="my-3">
          <h3 class="text-accent mb-4 text-base">Model Usage Breakdown</h3>
          <div
            class="grid grid-cols-2 gap-2 md:grid-cols-3 lg:grid-cols-4"
            x-show="getModelUsageData().length > 0"
          >
            <template x-for="(model, index) in getModelUsageData().slice(0, 8)" :key="model.name">
              <div
                class="bg-background-tertiary border-border relative flex flex-col gap-2 rounded-lg border p-3"
              >
                <code class="w-max text-sm font-bold" x-text="model.name"></code>
                <div
                  class="text-accent-light absolute top-3 right-3 text-sm"
                  x-text="'#' + (index + 1)"
                ></div>

                <div class="flex items-center justify-between gap-2">
                  <div class="text-sm" x-text="formatNumber(model.count) + ' requests'"></div>
                  <div class="text-accent-light text-sm" x-text="model.percentage + '%'"></div>
                </div>

                <div class="bg-border mt-2 h-1.5 w-full overflow-hidden rounded-sm">
                  <div
                    class="bg-accent h-full rounded-sm"
                    :style="'width: ' + model.percentage + '%'"
                  ></div>
                </div>
              </div>
            </template>
          </div>

          <div
            class="text-text-secondary p-4 text-center italic"
            x-show="getModelUsageData().length === 0"
          >
            No model usage data available
          </div>
        </div>
      </div>

      <!-- Recent Requests -->
      <div class="card" x-show="userData && !loading && userLogs.length > 0" id="logs-card">
        <div class="mb-6 flex items-center justify-between">
          <h2 class="text-xl font-bold">Recent Requests</h2>
          <div>
            <span>Your Ip:</span>
            <code x-text="formatIp(currentIp)"></code>
          </div>
        </div>

        <div class="mb-4 overflow-x-auto">
          <table
            class="bg-background border-background-secondary h-px w-full border-separate border-spacing-0 overflow-hidden rounded-lg border shadow-lg transition-all"
          >
            <thead class="bg-background-secondary">
              <tr>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  No.
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Model
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  IP
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Input
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Output
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Total
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Date
                </th>
              </tr>
            </thead>

            <tbody>
              <template x-for="(log, index) in paginatedLogs" :key="index">
                <tr
                  class="transition-colors hover:bg-[rgba(255,255,255,0.1)]"
                  :class="{ 'bg-background-tertiary': index % 2 === 1 }"
                >
                  <th
                    class="text-accent border-border border-b p-2 px-3"
                    x-text="(logsCurrentPage - 1) * logsPerPage + index + 1"
                  ></th>

                  <td class="border-border border-b p-2 px-3 text-sm">
                    <code x-text="log.payload.model"></code>
                  </td>

                  <td class="border-border border-b p-2 px-3">
                    <code
                      x-text="formatIp(log.payload.ip)"
                      :class="log.payload.ip === currentIp ? 'text-success' : 'text-danger'"
                    ></code>
                  </td>

                  <td class="border-border border-b p-2 px-3">
                    <div
                      x-bind:title="`$${getPricePerMillion(log.input.cost, log.input.tokens)}/1M tokens`"
                    >
                      <span x-text="formatNumber(log.input.tokens)"></span>
                      <span
                        class="text-accent-light text-sm"
                        x-text="formatCost(log.input.cost)"
                      ></span>
                    </div>
                  </td>
                  <td class="border-border border-b p-2 px-3">
                    <div
                      x-bind:title="`$${getPricePerMillion(log.output.cost, log.output.tokens)}/1M tokens`"
                    >
                      <span x-text="formatNumber(log.output.tokens)"></span>
                      <span
                        class="text-accent-light text-sm"
                        x-text="formatCost(log.output.cost)"
                      ></span>
                    </div>
                  </td>
                  <td class="border-border border-b p-2 px-3">
                    <div>
                      <span x-text="formatNumber(log.input.tokens + log.output.tokens)"></span>
                      <span
                        class="text-accent-light text-sm"
                        x-text="formatCost(log.input.cost + log.output.cost)"
                      ></span>
                    </div>
                  </td>
                  <td
                    class="border-border border-b p-2 px-3"
                    x-text="formatDate(log.createdAt)"
                  ></td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>

        <!-- Pagination controls for logs -->
        <div id="logs-pagination" class="mt-4 mb-2" x-show="userLogs.length > logsPerPage">
          <div class="flex items-center justify-center gap-4">
            <button
              class="bg-accent cursor-pointer rounded-lg border-none px-4 py-2 text-white transition-colors"
              @click="changePage('prev')"
              :disabled="logsCurrentPage === 1"
              :class="{ ' bg-[#25313c]! cursor-not-allowed!': logsCurrentPage === 1, 'hover:bg-accent-hover': logsCurrentPage !== 1 }"
            >
              &laquo; Prev
            </button>

            <span class="text-base">
              Page <span x-text="logsCurrentPage"></span> of
              <span x-text="totalLogsPages"></span>
            </span>

            <button
              class="bg-accent cursor-pointer rounded-lg border-none px-4 py-2 text-white transition-colors disabled:bg-[#25313c]"
              @click="changePage('next')"
              :disabled="logsCurrentPage === totalLogsPages"
              :class="{ ' bg-[#25313c]! cursor-not-allowed!': logsCurrentPage === totalLogsPages, 'hover:bg-accent-hover': logsCurrentPage !== totalLogsPages }"
            >
              Next &raquo;
            </button>
          </div>
        </div>
      </div>

      <!-- Edit Nickname Modal -->
      <div
        class="fixed top-0 left-0 z-[1000] h-full w-full items-center justify-center bg-[rgba(0,0,0,0.7)]"
        :class="{ 'flex': showModal && userData, 'hidden': !showModal || !userData }"
      >
        <div
          class="bg-background border-background-secondary w-[90%] max-w-[500px] rounded-lg border p-6 shadow-lg"
          x-show="userData"
        >
          <div class="mb-4 flex items-center justify-between">
            <h3 class="text-lg font-bold">Edit Nickname</h3>
            <button
              class="cursor-pointer border-none bg-transparent text-lg text-white"
              @click="showModal = false"
            >
              &times;
            </button>
          </div>

          <div class="mb-6">
            <div class="flex flex-col gap-2">
              <label for="editValue" class="text-accent font-bold">Nickname</label>
              <input
                type="text"
                id="editValue"
                x-model="editValue"
                placeholder="Enter nickname"
                class="border-background-secondary bg-background-tertiary rounded-lg border p-3 text-base text-white"
              />
            </div>
          </div>
          <div class="flex justify-end gap-4">
            <button
              class="bg-background-tertiary cursor-pointer rounded-lg px-4 py-2 text-base text-white"
              @click="showModal = false"
            >
              <span>❌</span> Cancel
            </button>
            <button
              class="bg-accent cursor-pointer rounded-lg px-4 py-2 text-base text-white"
              @click="saveChanges()"
            >
              <span>💾</span> Save
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("alpine:init", () => {
        Alpine.data("userLookup", () => ({
          token: "<%= user?.token || '' %>",
          loading: false,
          error: null,
          userData: null,
          userLogs: [],
          showModal: false,
          editValue: "",
          usageChart: null,
          currentIp: "<%= ip %>",

          // Pagination for logs
          logsCurrentPage: 1,
          logsPerPage: 10,

          dateFormat: new Intl.DateTimeFormat("en-US", { dateStyle: "medium", timeStyle: "short" }),

          init() {
            if (this.token) {
              this.lookupUser();
            }
          },

          reset() {
            this.token = "";
            this.error = null;
            this.userData = null;
            this.userLogs = [];

            // Clear the chart if it exists
            if (this.usageChart) {
              this.usageChart.destroy();
              this.usageChart = null;
            }

            fetch("/user/api/clear-token", { method: "POST" });
          },

          truncateToken(token) {
            if (!token) return "";

            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

            if (uuidRegex.test(token)) {
              const parts = token.split("-");
              return `${parts[0]}-${parts[1]}-${"x".repeat(parts[2].length)}-${"x".repeat(parts[3].length)}-${"x".repeat(parts[4].length)}`;
            } else {
              return token.replace(/[^-]/g, "x");
            }
          },

          lookupUser() {
            if (!this.token) {
              this.error = "Please enter a token";
              return;
            }

            this.loading = true;
            this.error = null;

            fetch("/user/api/lookup", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ token: this.token }),
            })
              .then((response) => {
                if (!response.ok) {
                  return response.json().then((data) => {
                    throw new Error(data.error || `HTTP error! Status: ${response.status}`);
                  });
                }
                return response.json();
              })
              .then((data) => {
                this.userData = data;
                this.fetchUserLogs();
              })
              .catch((err) => {
                this.loading = false;
                this.error = err.message;
                this.userData = null;
              });
          },

          fetchUserLogs() {
            if (!this.userData || !this.userData.token) {
              this.loading = false;
              return;
            }

            fetch(`/user/api/logs?token=${this.userData.token}`)
              .then((response) => {
                if (!response.ok) {
                  throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
              })
              .then((data) => {
                if (data.error) {
                  this.error = data.error.message || "Failed to fetch user logs";
                } else {
                  this.userLogs = data.logs || [];

                  // Initialize chart after logs are loaded
                  this.$nextTick(() => this.initChart());
                }
                this.loading = false;
              })
              .catch((err) => {
                this.loading = false;
                this.error = "Failed to fetch user logs: " + err.message;
              });
          },

          // Group logs by date
          groupLogsByDate() {
            if (!this.userLogs || !this.userLogs.length) return {};

            const grouped = {};

            this.userLogs.forEach((log) => {
              const date = new Date(log.createdAt);
              const dateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD format

              if (!grouped[dateStr]) {
                grouped[dateStr] = {
                  count: 0,
                  inputTokens: 0,
                  outputTokens: 0,
                  inputCost: 0,
                  outputCost: 0,
                  totalCost: 0,
                };
              }

              grouped[dateStr].count++;
              grouped[dateStr].inputTokens += log.input.tokens || 0;
              grouped[dateStr].outputTokens += log.output.tokens || 0;
              grouped[dateStr].inputCost += log.input.cost || 0;
              grouped[dateStr].outputCost += log.output.cost || 0;
              grouped[dateStr].totalCost += (log.input.cost || 0) + (log.output.cost || 0);
            });

            return grouped;
          },

          initChart() {
            if (this.usageChart) {
              this.usageChart.destroy();
            }

            if (!this.userLogs || !this.userLogs.length) return;

            const groupedData = this.groupLogsByDate();
            const dates = Object.keys(groupedData).sort();

            // Prepare data for the chart
            const requestCounts = dates.map((date) => groupedData[date].count);
            const inputTokens = dates.map((date) => groupedData[date].inputTokens);
            const outputTokens = dates.map((date) => groupedData[date].outputTokens);
            const totalCosts = dates.map((date) => groupedData[date].totalCost);

            // Format dates for display
            const formattedDates = dates.map((date) => {
              const [year, month, day] = date.split("-");
              return `${month}/${day}`;
            });

            const ctx = document.getElementById("usageChart");

            this.usageChart = new Chart(ctx, {
              type: "bar",
              data: {
                labels: formattedDates,
                datasets: [
                  {
                    label: "Requests",
                    data: requestCounts,
                    backgroundColor: "rgba(54, 162, 235, 0.5)",
                    borderColor: "rgba(54, 162, 235, 1)",
                    borderWidth: 1,
                    yAxisID: "y",
                  },
                  {
                    label: "Input Tokens",
                    data: inputTokens,
                    backgroundColor: "rgba(75, 192, 192, 0.5)",
                    borderColor: "rgba(75, 192, 192, 1)",
                    borderWidth: 1,
                    yAxisID: "y1",
                  },
                  {
                    label: "Output Tokens",
                    data: outputTokens,
                    backgroundColor: "rgba(153, 102, 255, 0.5)",
                    borderColor: "rgba(153, 102, 255, 1)",
                    borderWidth: 1,
                    yAxisID: "y1",
                  },
                  {
                    label: "Total Cost ($)",
                    data: totalCosts,
                    backgroundColor: "rgba(255, 159, 64, 0.5)",
                    borderColor: "rgba(255, 159, 64, 1)",
                    borderWidth: 1,
                    yAxisID: "y2",
                    type: "line",
                    fill: false,
                    tension: 0.4,
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    type: "linear",
                    display: true,
                    position: "left",
                    title: {
                      display: true,
                      text: "Requests",
                    },
                    beginAtZero: true,
                  },
                  y1: {
                    type: "linear",
                    display: true,
                    position: "right",
                    title: {
                      display: true,
                      text: "Tokens",
                    },
                    beginAtZero: true,
                    grid: {
                      drawOnChartArea: false,
                    },
                  },
                  y2: {
                    type: "linear",
                    display: true,
                    position: "right",
                    title: {
                      display: true,
                      text: "Cost ($)",
                    },
                    beginAtZero: true,
                    grid: {
                      drawOnChartArea: false,
                    },
                  },
                },
                plugins: {
                  tooltip: {
                    callbacks: {
                      label: function (context) {
                        let label = context.dataset.label || "";
                        if (label) label += ": ";

                        if (context.parsed.y !== null) {
                          if (label.includes("Cost")) {
                            label += "$" + context.parsed.y.toFixed(6);
                          } else {
                            label += new Intl.NumberFormat().format(context.parsed.y);
                          }
                        }
                        return label;
                      },
                    },
                  },
                },
              },
            });
          },

          formatIp(ip) {
            if (!ip) return "";
            if (ip.startsWith("ip-")) return ip.slice(0, 13);
            return ip;
          },

          formatNumber(num) {
            if (num === undefined || num === null) return "0";
            return num.toLocaleString();
          },

          formatCost(cost) {
            if (cost === undefined || cost === null) return "$0.000000";
            return "$" + cost.toFixed(6);
          },

          getPricePerMillion(cost, tokens) {
            if (!tokens || tokens === 0) return 0;
            return ((cost / tokens) * 1_000_000).toFixed(2);
          },

          // Computed properties for pagination
          get paginatedLogs() {
            if (!this.userLogs || !this.userLogs.length) return [];
            const startIndex = (this.logsCurrentPage - 1) * this.logsPerPage;
            const endIndex = startIndex + this.logsPerPage;
            return this.userLogs.slice(startIndex, endIndex);
          },

          get totalLogsPages() {
            if (!this.userLogs || !this.userLogs.length) return 1;
            return Math.ceil(this.userLogs.length / this.logsPerPage);
          },

          // Method to change page and scroll to pagination controls
          changePage(direction) {
            if (direction === "prev") {
              this.logsCurrentPage = Math.max(1, this.logsCurrentPage - 1);
            } else if (direction === "next") {
              this.logsCurrentPage = Math.min(this.totalLogsPages, this.logsCurrentPage + 1);
            }

            // Scroll to the pagination container after a short delay to allow DOM update
            this.$nextTick(() => {
              const paginationElement = document.getElementById("logs-pagination");
              if (paginationElement) {
                // Scroll with smooth behavior
                paginationElement.scrollIntoView({ behavior: "smooth", block: "center" });
              }
            });
          },

          // Statistics calculation methods
          getTotalPrompts() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.length;
          },

          getTotalTokens() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.input.tokens || 0) + (log.output.tokens || 0);
            }, 0);
          },

          getTotalInputTokens() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.input.tokens || 0);
            }, 0);
          },

          getTotalOutputTokens() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.output.tokens || 0);
            }, 0);
          },

          getTotalCost() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.input.cost || 0) + (log.output.cost || 0);
            }, 0);
          },

          getInputCost() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.input.cost || 0);
            }, 0);
          },

          getOutputCost() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.output.cost || 0);
            }, 0);
          },

          getLastRequestDate() {
            if (!this.userLogs || !this.userLogs.length) return "Never";

            // Sort logs by timestamp (descending)
            const sortedLogs = [...this.userLogs].sort((a, b) => {
              return new Date(b.createdAt) - new Date(a.createdAt);
            });

            // Return formatted date of the most recent log
            return this.formatDate(sortedLogs[0].createdAt);
          },

          getLastRequestModel() {
            if (!this.userLogs || !this.userLogs.length) return null;

            // Sort logs by timestamp (descending)
            const sortedLogs = [...this.userLogs].sort((a, b) => {
              return new Date(b.createdAt) - new Date(a.createdAt);
            });

            // Return model of the most recent log
            return sortedLogs[0].payload.model;
          },

          getMostUsedModel() {
            if (!this.userLogs || !this.userLogs.length) return { model: null, count: 0 };

            // Get model counts
            const modelCounts = this.getModelCounts();

            // Find the model with the highest count
            let mostUsedModel = null;
            let highestCount = 0;

            Object.entries(modelCounts).forEach(([model, count]) => {
              if (count > highestCount) {
                mostUsedModel = model;
                highestCount = count;
              }
            });

            return { model: mostUsedModel, count: highestCount };
          },

          getModelCounts() {
            if (!this.userLogs || !this.userLogs.length) return {};

            // Count occurrences of each model
            const modelCounts = {};
            this.userLogs.forEach((log) => {
              const model = log.payload.model;
              if (!modelCounts[model]) {
                modelCounts[model] = 0;
              }
              modelCounts[model]++;
            });

            return modelCounts;
          },

          getModelUsageData() {
            if (!this.userLogs || !this.userLogs.length) return [];

            // Get model counts
            const modelCounts = this.getModelCounts();
            const totalRequests = this.getTotalPrompts();

            // Convert to array and calculate percentages
            const modelData = Object.entries(modelCounts).map(([name, count]) => {
              const percentage = Math.round((count / totalRequests) * 100);
              return { name, count, percentage };
            });

            // Sort by count (descending)
            return modelData.sort((a, b) => b.count - a.count);
          },

          openEditModal(value) {
            this.editValue = value || "";
            this.showModal = true;
          },

          saveChanges() {
            if (!this.userData) {
              this.showModal = false;
              return;
            }

            fetch("/user/api/edit-nickname", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                token: this.userData.token,
                nickname: this.editValue,
              }),
            })
              .then((response) => {
                if (!response.ok) {
                  return response.json().then((data) => {
                    throw new Error(
                      data.error?.message || `HTTP error! Status: ${response.status}`
                    );
                  });
                }
                return response.json();
              })
              .then((data) => {
                if (data.error) {
                  this.error = data.error.message || "Failed to update nickname";
                } else {
                  // Update the local data
                  this.userData.nickname = this.editValue;
                  this.showModal = false;
                }
              })
              .catch((err) => {
                this.error = "Failed to update nickname: " + err.message;
              });
          },

          formatDate(timestamp) {
            if (!timestamp) return null;
            const date = new Date(timestamp);
            return this.dateFormat.format(date);
          },
        }));
      });
    </script>
  </body>
</html>
