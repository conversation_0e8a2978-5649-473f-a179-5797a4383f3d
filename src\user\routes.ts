import express, { Router } from "express";

import { checkCsrfToken, injectCsrfToken } from "@/shared/inject-csrf";
import { injectLocals } from "@/shared/inject-locals";
import { withSession } from "@/shared/with-session";

import { selfServiceAPIRouter } from "./api/self-services-api";
import { selfServiceRouter } from "./web/self-service";

const userRouter = Router();

userRouter.use(
  express.json({ limit: "1mb" }),
  express.urlencoded({ extended: true, limit: "1mb" })
);
userRouter.use(withSession);
userRouter.use(injectCsrfToken);

userRouter.use("/api", selfServiceAPIRouter);

userRouter.use(checkCsrfToken);
userRouter.use(injectLocals);
userRouter.use(selfServiceRouter);

userRouter.use(
  (err: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
    const data: any = { message: err.message, stack: err.stack, status: 500 };
    const isCsrfError = err.message === "invalid csrf token";

    if (isCsrfError) {
      res.clearCookie("csrf");
      req.session.csrf = undefined;
    }

    const message = isCsrfError ? "CSRF token mismatch; try refreshing the page" : err.message;
    return res.status(500).json({ error: { message } });
  }
);

export { userRouter };
