import chokidar from "chokidar";
import fs from "fs";

import { config } from "./config";
import { getServerTitle } from "./info-page";
import { logger } from "./logger";
import type { ServiceInfo } from "./service-info";

import { getUserPublicData } from "./shared/users/user-store";

const log = logger.child({ module: "custom-info" });

export async function getUsersData() {
  if (config.gatekeeper === "user_token") return await getUserPublicData();
  return [];
}

const indexPagePath = "./public/index.html";
let customPageCache: string | undefined = undefined;

const watcher = chokidar.watch(indexPagePath, { persistent: true });
watcher.on("change", (path) => {
  log.info({ filename: path }, "Custom page file changed, updating cache");
  customPageCache = fs.readFileSync(indexPagePath, "utf-8");
});

watcher.on("error", (error) => {
  log.error("Error watching file:", error);
  // Remove the cache so we never serve stale data
  customPageCache = undefined;
});

function getCustomPageFile() {
  if (customPageCache) return customPageCache;

  const customPage = fs.readFileSync(indexPagePath, "utf-8");
  customPageCache = customPage;

  return customPage;
}

export async function renderCustomPage(info: ServiceInfo) {
  const title = getServerTitle();
  const users = await getUsersData();

  let customPage = getCustomPageFile();

  if (customPage.includes("{{title}}")) {
    customPage = customPage.replaceAll("{{title}}", title);
  }
  if (customPage.includes("{{info:json}}")) {
    customPage = customPage.replaceAll("{{info:json}}", JSON.stringify(info));
  }
  if (customPage.includes("{{users:json}}")) {
    customPage = customPage.replaceAll("{{users:json}}", JSON.stringify(users));
  }

  return customPage;
}
