import { type <PERSON><PERSON><PERSON><PERSON><PERSON>, Router } from "express";

import { cacheStore } from "@/shared/cache";
import { keyPool, type <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/shared/key-management";
import { getOpenAIModelFamily, isAllowedModel } from "@/shared/models";
import { createModelList } from "@/shared/utils";

import {
  addKey,
  addKeyForEmbeddingsRequest,
  createEmbeddingsPreprocessorMiddleware,
  createPreprocessorMiddleware,
  finalizeBody,
} from "../middleware/request";
import { createQueuedProxyMiddleware } from "../middleware/request/proxy-middleware-factory";
import type { ProxyResHandlerWithBody } from "../middleware/response";
import { ipLimiter } from "../rate-limit";

// https://platform.openai.com/docs/models/overview
export function generateModelList() {
  const provider = keyPool.getKeyProvider("openai");
  if (provider.available() === 0) return [];

  const keys = provider.list() as OpenAIKey[];
  const modelFamilies = new Set(keys.flatMap((k) => k.modelFamilies).filter(isAllowedModel));
  const modelIds = new Set(keys.flatMap((k) => k.modelIds));

  return createModelList(Array.from(modelIds), "openai", (id) => {
    const allowed = modelFamilies.has(getOpenAIModelFamily(id)) && isAllowedModel(id);
    const known = ["gpt", "codex", "o1", "o3", "o4", "chatgpt", "text-embedding"].some((prefix) =>
      id.startsWith(prefix)
    );
    const isFinetune = id.includes("ft");
    return allowed && known && !isFinetune;
  });
}

const handleModelRequest: RequestHandler = async (_req, res) => {
  const cache = await cacheStore.get<ReturnType<typeof generateModelList>>("openai");

  if (cache) {
    res.setHeader("Cache-State", "HIT");
    return res.status(200).json({ object: "list", data: cache });
  }

  const models = generateModelList();
  await cacheStore.set("openai", models);

  res.setHeader("Cache-State", "MISS");
  return res.status(200).json({ object: "list", data: models });
};

const openaiResponseHandler: ProxyResHandlerWithBody = async (_proxyRes, req, res, body) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  let newBody = body;
  res.status(200).json({ ...newBody, proxy: body.proxy });
};

const openaiProxy = createQueuedProxyMiddleware({
  mutations: [addKey, finalizeBody],
  target: "https://api.openai.com",
  blockingResponseHandler: openaiResponseHandler,
});

const openaiEmbeddingsProxy = createQueuedProxyMiddleware({
  mutations: [addKeyForEmbeddingsRequest, finalizeBody],
  target: "https://api.openai.com",
});

const openaiRouter = Router();
openaiRouter.get("/v1/models", handleModelRequest);

// General chat completion endpoint. Turbo-instruct is not supported here.
openaiRouter.post(
  "/v1/chat/completions",
  ipLimiter,
  createPreprocessorMiddleware({ inboundApi: "openai", outboundApi: "openai", service: "openai" }),
  openaiProxy
);

openaiRouter.post(
  "/v1/responses",
  ipLimiter,
  createPreprocessorMiddleware({
    inboundApi: "openai-response",
    outboundApi: "openai-response",
    service: "openai",
  }),
  openaiProxy
);

// Embeddings endpoint.
openaiRouter.post(
  "/v1/embeddings",
  ipLimiter,
  createEmbeddingsPreprocessorMiddleware(),
  openaiEmbeddingsProxy
);

export const openai = openaiRouter;
