import { logger } from "@/logger";

import type { StreamingCompletionTransformer } from "../index";
import { parseEvent, type ServerSentEvent } from "../parse-sse";

const log = logger.child({
  module: "sse-transformer",
  transformer: "google-ai-to-openai",
});

type GoogleAIStreamEvent = {
  modelVersion: string;
  responseId?: string;
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
    promptTokensDetails: { modality: string; tokenCount: number }[];
    thoughtsTokenCount: number;
  };
  candidates: {
    content?: { parts?: { text: string; thought?: boolean }[]; role: string };
    finishReason?: "STOP" | "MAX_TOKENS" | "SAFETY" | "RECITATION" | "OTHER" | "PROHIBITED_CONTENT";
    index: number;
    tokenCount?: number;
    safetyRatings: { category: string; probability: string }[];
  }[];
};

/**
 * Transforms an incoming Google AI SSE to an equivalent OpenAI
 * chat.completion.chunk SSE.
 */
export const googleAIToOpenAI: StreamingCompletionTransformer = (params) => {
  const { data, lastEvent } = params;

  const rawEvent = parseEvent(data);
  if (!rawEvent.data || rawEvent.data === "[DONE]") {
    return { position: -1 };
  }

  const lastCompletionEvent = lastEvent ? asCompletion(parseEvent(lastEvent)) : null;
  const completionEvent = asCompletion(rawEvent);
  if (!completionEvent) {
    return { position: -1 };
  }

  const parts = completionEvent.candidates[0].content?.parts || [];
  let content = parts[0]?.text ?? "";

  if (isSafetyStop(completionEvent)) {
    const event = structuredClone(completionEvent);

    delete event.responseId;
    event.modelVersion = event.modelVersion.replace("models/", "");

    content =
      "\n---\n[Proxy Warning] Gemini safety filter triggered.\n" +
      "```json\n" +
      JSON.stringify(event, null, 2) +
      "\n```";
  }

  // Add <thinking> tag if the last event is not a thought but the current event is.
  if (!lastCompletionEvent && parts[0].thought) {
    content = "<thinking>\n" + content;
  }

  // Add </thinking> tag if the last event is a thought but the current event is not.
  if (lastCompletionEvent?.candidates?.[0]?.content?.parts?.[0].thought && !parts[0].thought) {
    content = "\n</thinking>\n" + content;
  }

  const model = completionEvent.modelVersion.replace("models/", "") || params.fallbackModel;
  const finish_reason = completionEvent.candidates[0].finishReason?.toLowerCase() ?? null;

  // Only send back usage object in the final event.
  const usage =
    completionEvent.usageMetadata && finish_reason === "stop"
      ? {
          prompt_tokens: completionEvent.usageMetadata.promptTokenCount,
          completion_tokens: completionEvent.usageMetadata.candidatesTokenCount,
          total_tokens: completionEvent.usageMetadata.totalTokenCount,

          completion_tokens_details: {
            reasoning_tokens: completionEvent.usageMetadata.thoughtsTokenCount ?? 0,
          },
        }
      : null;

  const newEvent = {
    id: "goo-" + (completionEvent.responseId ?? params.fallbackId),
    object: "chat.completion.chunk" as const,
    created: Date.now(),
    model,
    usage,
    choices: [{ index: 0, delta: { content }, finish_reason }],
  };

  return { position: -1, event: newEvent };
};

function isSafetyStop(completion: GoogleAIStreamEvent) {
  const isSafetyStop = ["SAFETY", "OTHER", "PROHIBITED_CONTENT"].includes(
    completion.candidates[0].finishReason ?? ""
  );
  const hasNoContent =
    typeof completion.candidates[0].content === "undefined" ||
    completion.candidates[0].content?.parts?.[0].text.length === 0;

  return isSafetyStop && hasNoContent;
}

function asCompletion(event: ServerSentEvent): GoogleAIStreamEvent | null {
  try {
    const parsed = JSON.parse(event.data.trim()) as GoogleAIStreamEvent;
    if (parsed.candidates?.length > 0) {
      return parsed;
    } else {
      return null;
    }
  } catch (error) {
    log.warn({ error: error.stack, event }, "Received invalid event");
  }
  return null;
}
