import pino from "pino";
import { Transform, type TransformOptions } from "stream";

import type { APIFormat } from "@/shared/key-management";

type SSEStreamAdapterOptions = TransformOptions & {
  contentType?: string;
  api: APIFormat;
  logger: pino.Logger;
};

/**
 * Receives a stream of events in a variety of formats and transforms them into
 * Server-Sent Events.
 *
 * This is an object-mode stream, so it expects to receive objects and will emit
 * strings.
 */
export class SSEStreamAdapter extends Transform {
  private api: APIFormat;
  private partialMessage = "";
  private textDecoder = new TextDecoder("utf-8");
  private log: pino.Logger;

  constructor(options: SSEStreamAdapterOptions) {
    super({ ...options, objectMode: true });
    this.api = options.api;
    this.log = options.logger.child({ module: "sse-stream-adapter" });
  }

  _transform(data: any, _enc: string, callback: (err?: Error | null) => void) {
    try {
      // `data` is a string, but possibly only a partial message
      const fullMessages = (this.partialMessage + data).split(/\r\r|\n\n|\r\n\r\n/);
      this.partialMessage = fullMessages.pop() || "";

      for (const message of fullMessages) {
        // Mixing line endings will break some clients and our request queue
        // will have already sent \n for heartbeats, so we need to normalize
        // to \n.
        this.push(message.replace(/\r\n?/g, "\n") + "\n\n");
      }

      callback();
    } catch (error) {
      error.lastEvent = data?.toString();
      this.emit("error", error);
      callback(error);
    }
  }

  _flush(callback: (err?: Error | null) => void) {
    callback();
  }
}
