<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "View Key" }) %>
  </head>

  <body class="bg-background font-mono text-white">
    <div x-data="keyView" class="mx-auto flex w-full max-w-7xl flex-col gap-4 p-4">
      <h1 class="mt-2 mb-6 text-center text-3xl font-bold">Key Details</h1>

      <!-- Loading state -->
      <div class="flex flex-col items-center justify-center p-4 text-center" x-show="loading">
        <div
          class="border-accent h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"
        ></div>
        <p>Loading key data...</p>
      </div>

      <!-- Error message -->
      <div
        class="border-border-error bg-background-error text-color-error my-4 rounded-lg border p-4"
        x-show="error"
        x-text="error"
      ></div>

      <!-- Key information -->
      <template x-if="keyData && !loading">
        <div class="card space-y-4 p-4">
          <div class="w-full text-center">
            <h2 class="text-3xl font-bold">Key Information</h2>
          </div>

          <div class="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Hash:</span>
              <code class="w-max text-sm" x-text="keyData.hash"></code>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Service:</span>
              <span class="w-max text-sm" x-text="keyData.service"></span>
            </div>

            <template x-if="keyData.service === 'openai'">
              <div class="flex flex-col gap-2">
                <span class="text-accent font-bold">Organization ID:</span>
                <code class="w-max text-sm" x-text="keyData.organizationId ?? 'No Org'"></code>
              </div>
            </template>

            <div class="col-span-full flex flex-col gap-2">
              <span class="text-accent font-bold">Model Families:</span>
              <div class="flex flex-wrap gap-1 gap-x-2">
                <template
                  x-for="model in keyData.modelFamilies.sort((a, b) => a.localeCompare(b))"
                  :key="model"
                >
                  <code class="w-max text-xs" x-text="model"></code>
                </template>
              </div>
            </div>

            <div class="col-span-full flex flex-col gap-2">
              <span class="text-accent font-bold">Model IDs:</span>
              <div class="flex flex-wrap gap-1 gap-x-2">
                <template x-for="model in keyData.modelIds" :key="model">
                  <code class="w-max text-xs" x-text="model"></code>
                </template>
              </div>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Added:</span>
              <span x-text="formatDate(keyData.addedAt)"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Last Used:</span>
              <span x-text="formatDate(keyData.lastUsedAt) || 'Never'"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Last Checked:</span>
              <span x-text="formatDate(keyData.lastCheckedAt) || 'Never'"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Status:</span>
              <div>
                <span
                  x-text="keyData.isDisabled ? 'Disabled' : 'Active'"
                  :class="keyData.isDisabled ? 'text-danger' : 'text-success'"
                ></span>

                <template x-if="keyData.service === 'anthropic'">
                  <span x-show="keyData.isPozzed">Pozzed</span>
                </template>
              </div>
            </div>

            <div class="flex flex-col gap-2" x-show="keyData.isDisabled">
              <span class="text-accent font-bold">Reason:</span>
              <span x-text="getDisabledReason()"></span>
            </div>

            <template x-if="'tier' in keyData">
              <div class="flex flex-col gap-2">
                <span class="text-accent font-bold">Tier:</span>
                <code class="w-max text-sm capitalize" x-text="keyData.tier"></code>
              </div>
            </template>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Rate Limit At:</span>
              <code class="w-max text-sm" x-text="keyData.rateLimitedAt"></code>
            </div>

            <template x-if="keyData.service === 'openai'">
              <div class="contents">
                <div class="flex flex-col gap-2">
                  <span class="text-accent font-bold">Verified:</span>
                  <code class="w-max text-sm" x-text="keyData.isVerified ? 'Yes' : 'No'"></code>
                </div>

                <div class="flex flex-col gap-2">
                  <span class="text-accent font-bold">Rate Limit Requests Reset:</span>
                  <code class="w-max text-sm" x-text="keyData.rateLimitRequestsReset"></code>
                </div>

                <div class="flex flex-col gap-2">
                  <span class="text-accent font-bold">Rate Limit Tokens Reset:</span>
                  <code class="w-max text-sm" x-text="keyData.rateLimitTokensReset"></code>
                </div>
              </div>
            </template>

            <template x-if="keyData.service === 'anthropic'">
              <div class="flex flex-col gap-2">
                <span class="text-accent font-bold">Rate Limit Until:</span>
                <code class="w-max text-sm" x-text="keyData.rateLimitedUntil"></code>
              </div>
            </template>
          </div>
        </div>
      </template>

      <!-- Usage Statistics -->
      <template x-if="keyData && !loading">
        <div class="card space-y-4 p-4">
          <div class="w-full text-center">
            <h2 class="text-3xl font-bold">Usage Statistics</h2>
          </div>

          <div class="grid w-full grid-cols-2 items-center justify-center gap-4">
            <div class="card bg-background-tertiary flex items-center justify-between gap-2">
              <div class="text-accent">Input Tokens</div>
              <span class="card-value" x-text="formatNumber(keyData.input.tokens)"></span>
            </div>

            <div class="card bg-background-tertiary flex items-center justify-between gap-2">
              <div class="text-accent">Output Tokens</div>
              <div class="card-value" x-text="formatNumber(keyData.output.tokens)"></div>
            </div>

            <div class="card bg-background-tertiary flex items-center justify-between gap-2">
              <div class="text-accent">Prompts</div>
              <div class="card-value" x-text="formatNumber(keyData.prompts)"></div>
            </div>

            <div class="card bg-background-tertiary flex items-center justify-between gap-2">
              <div class="text-accent">Total Tokens</div>
              <div
                class="card-value"
                x-text="formatNumber(keyData.input.tokens + keyData.output.tokens)"
              ></div>
            </div>
          </div>

          <div class="w-full space-y-4 text-center">
            <h2 class="text-3xl font-bold">Model Family Usage</h2>

            <div class="grid grid-cols-2 gap-4" id="modelFamilyCards">
              <template x-for="model in getModelFamilyStats()" :key="model.name">
                <div class="card bg-background-tertiary flex flex-col gap-2 p-3">
                  <code class="w-max text-xs" x-text="model.name"></code>

                  <div class="grid grid-cols-2 gap-2 gap-x-4 text-sm">
                    <div class="flex items-center justify-between gap-2">
                      <span>Input:</span>
                      <div
                        class="flex items-center gap-1"
                        x-bind:title="getPricePerMillion(model.input.cost, model.input.tokens)"
                      >
                        <span x-text="formatNumber(model.input.tokens)"></span>
                        <span class="cost text-xs" x-text="formatCost(model.input.cost)"></span>
                      </div>
                    </div>

                    <div class="flex items-center justify-between gap-2">
                      <span>Output:</span>
                      <div
                        class="flex items-center gap-1"
                        x-bind:title="getPricePerMillion(model.output.cost, model.output.tokens)"
                      >
                        <span x-text="formatNumber(model.output.tokens)"></span>
                        <span class="cost text-xs" x-text="formatCost(model.output.cost)"></span>
                      </div>
                    </div>

                    <div class="flex items-center justify-between gap-2">
                      <span>Prompts:</span>
                      <span x-text="formatNumber(model.prompts)"></span>
                    </div>

                    <div class="flex items-center justify-between gap-2">
                      <span>Total:</span>
                      <div class="flex items-center gap-1">
                        <span
                          x-text="formatNumber(model.input.tokens + model.output.tokens)"
                        ></span>
                        <span
                          class="cost text-xs"
                          x-text="formatCost(model.input.cost + model.output.cost)"
                        ></span>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </template>

      <!-- Floating Refresh Button -->
      <div
        x-show="keyData"
        class="secondary-button fixed right-4 bottom-4 flex size-13 items-center justify-center rounded-full text-lg"
        :class="{ 'refreshing': isRefreshing }"
        @click="refreshData()"
        title="Refresh data"
      >
        <span>🔄</span>
      </div>

      <!-- prettier-ignore -->
      <%- include("partials/admin-footer", { url: "/admin/manage/list-keys?service=" + params.service, text: "Back to Keys" }) %>
    </div>

    <script>
      document.addEventListener("alpine:init", () => {
        Alpine.data("keyView", () => ({
          service: "<%= params.service %>",
          hash: "<%= params.hash %>",
          keyData: null,
          loading: true,
          error: null,
          isRefreshing: false,

          dateFormat: new Intl.DateTimeFormat("en-US", { dateStyle: "medium", timeStyle: "short" }),
          numberFormat: new Intl.NumberFormat(),

          init() {
            if (this.service && this.hash) {
              this.fetchKeyData();
            } else {
              this.error = "Missing service or key hash";
              this.loading = false;
            }
          },

          fetchKeyData(showLoading = true) {
            this.loading = showLoading;
            this.error = null;

            fetch(`/admin/api/keys/${this.service}/${this.hash}`)
              .then((response) => {
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                return response.json();
              })
              .then((data) => {
                this.loading = false;
                if (data.error) {
                  this.error = data.error.message || "Failed to fetch key data";
                  this.keyData = null;
                } else {
                  this.keyData = data;
                  this.error = null;
                }
              })
              .catch((err) => {
                this.loading = false;
                this.error = "Failed to fetch key data: " + err.message;
                this.keyData = null;
              });
          },

          refreshData() {
            // Prevent multiple refreshes
            if (this.isRefreshing) return;

            this.isRefreshing = true;
            this.error = null;

            fetch(`/admin/api/keys/${this.service}/${this.hash}`)
              .then((response) => {
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                return response.json();
              })
              .then((data) => {
                if (data.error) {
                  this.error = data.error.message || "Failed to fetch key data";
                  throw new Error(this.error);
                } else {
                  this.keyData = data;

                  // Show success message
                  const originalTitle = document.title;
                  document.title = "✓ Data refreshed!";
                  setTimeout(() => {
                    document.title = originalTitle;
                  }, 2000);
                }
              })
              .catch((err) => {
                this.error = "Failed to refresh data: " + err.message;
              })
              .finally(() => {
                this.isRefreshing = false;
              });
          },

          getDisabledReason() {
            if (!this.keyData) return null;

            if (this.keyData.isRevoked) return "Revoked";
            if (this.keyData.isOverQuota) return "Over Quota";

            return null;
          },

          getModelFamilyStats() {
            if (!this.keyData) return [];
            const statsByModel = {};

            // First, collect all token keys and organize them by model family
            Object.keys(this.keyData).forEach((k) => {
              if (k.endsWith("-usages")) {
                const modelName = k.slice(0, k.lastIndexOf("-"));
                statsByModel[modelName] = this.keyData[k];
              }
            });

            const modelFamilies = Object.entries(statsByModel)
              .map(([name, stats]) => ({ name, ...stats }))
              .sort((a, b) => a.name.localeCompare(b.name));

            return modelFamilies;
          },

          get modelIds() {
            const validPrefix = ["gpt", "codex", "o1", "o3", "o4", "chatgpt", "text-embedding"];
            return this.keyData.modelIds
              .filter(
                (id) => validPrefix.some((prefix) => id.startsWith(prefix)) && !id.includes("ft")
              )
              .sort((a, b) => a.localeCompare(b));
          },

          formatNumber(num) {
            if (num === undefined || num === null) return "0";
            return this.numberFormat.format(num);
          },

          formatCost(cost) {
            if (cost === undefined || cost === null) return "$0.000000";
            return "$" + cost.toFixed(6);
          },

          formatDate(timestamp) {
            if (!timestamp) return null;
            const date = new Date(timestamp);
            return this.dateFormat.format(date);
          },

          getPricePerMillion(cost, tokens) {
            if (!tokens || tokens === 0) return "";
            const pricePer1M = ((cost / tokens) * 1_000_000).toFixed(2);

            return `$${pricePer1M}/1M tokens`;
          },
        }));
      });
    </script>
  </body>
</html>
