/** This whole module kinda sucks */
import express, { Router, type Request } from "express";
import fs from "fs";
import showdown from "showdown";

import { config } from "./config";
import { getUsersData, renderCustomPage } from "./custom-info";
import { buildInfo, type ServiceInfo } from "./service-info";

import { cacheStore } from "./shared/cache";
import { checkCsrfToken, injectCsrfToken } from "./shared/inject-csrf";
import { keyPool } from "./shared/key-management";
import {
  isAllowedModel,
  MODEL_FAMILIES,
  MODEL_FAMILY_SERVICE,
  type ModelFamily,
} from "./shared/models";
import { withSession } from "./shared/with-session";

const INFO_PAGE_TTL = 2000;
const MODEL_FAMILY_FRIENDLY_NAME: { [f in ModelFamily]: string } = {
  // OpenAI
  turbo: "GPT-4o Mini / 3.5 Turbo",
  gpt4: "GPT-4",
  "gpt4-turbo": "GPT-4 Turbo",
  "gpt4.5": "GPT-4.5",
  "gpt4.1": "GPT-4.1",
  "gpt4.1-mini": "GPT-4.1 Mini",
  "gpt4.1-nano": "GPT-4.1 Nano",
  gpt4o: "GPT-4o",
  "gpt4o-mini": "GPT-4o Mini",
  "chatgpt-4o": "ChatGPT 4o",
  "codex-mini": "Codex Mini",
  o1: "OpenAI o1",
  "o1-pro": "OpenAI o1 Pro",
  "o1-mini": "OpenAI o1 mini",
  o3: "OpenAI o3",
  "o3-pro": "OpenAI o3 Pro",
  "o3-mini": "OpenAI o3 mini",
  "o4-mini": "OpenAI o4 mini",

  // Anthropic
  "claude-haiku": "Claude (Haiku)",
  "claude-sonnet": "Claude (Sonnet)",
  "claude-opus": "Claude (Opus)",

  // Google
  "gemini-flash-lite": "Gemini Flash Lite",
  "gemini-flash": "Gemini Flash",
  "gemini-pro": "Gemini Pro",

  // Deepseek
  "deepseek-chat": "Deepseek Chat",
  "deepseek-reasoner": "Deepseek Reasoner",

  // XAI
  "grok-4": "Grok 4",
  "grok-3": "Grok 3",
  "grok-3-mini": "Grok 3 Mini",
  "grok-3-fast": "Grok 3 Fast",
  "grok-3-mini-fast": "Grok 3 Mini Fast",
  "grok-2": "Grok 2",

  // Groq
  qwen3: "Qwen 3",
  "kimi-k2": "Kimi K2",
  "deepseek-r1-distill": "DeepSeek R1 Distill",
};

const converter = new showdown.Converter();
const customGreeting = fs.existsSync("greeting.md")
  ? `<div id="servergreeting">${fs.readFileSync("greeting.md", "utf8")}</div>`
  : "";

export const getBaseUrl = (req: Request) => {
  return req.protocol + "://" + req.get("host");
};

export const handleInfoPage: express.RequestHandler = async (req, res) => {
  const cachedPage = await cacheStore.get<string>("info-page");
  if (cachedPage) return res.send(cachedPage);

  const baseUrl = getBaseUrl(req);
  const isCustomPage = fs.existsSync("./public/index.html");

  const info = await buildInfo(baseUrl + config.proxyEndpointRoute);
  const infoPageHtml = isCustomPage ? await renderCustomPage(info) : renderPage(info);

  await cacheStore.set("info-page", infoPageHtml, INFO_PAGE_TTL);

  return res.send(infoPageHtml);
};

export function renderPage(info: ServiceInfo) {
  const title = getServerTitle();
  const headerHtml = buildInfoPageHeader(info);

  return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="robots" content="noindex" />
    <title>${title}</title>
    <link rel="stylesheet" href="/res/css/reset.css" media="screen" />
    <link rel="stylesheet" href="/res/css/sakura.css" media="screen" />
    <link rel="stylesheet" href="/res/css/sakura-dark.css" media="screen and (prefers-color-scheme: dark)" />
    <style>
      body {
        font-family: sans-serif;
        padding: 1em;
        max-width: 900px;
        margin: 0;
      }

      .self-service-links {
        display: flex;
        justify-content: center;
        margin-bottom: 1em;
        padding: 0.5em;
        font-size: 0.8em;
      }

      .self-service-links a {
        margin: 0 0.5em;
      }
    </style>
  </head>
  <body>
    ${headerHtml}
    <hr />
    ${getSelfServiceLinks()}
    <h2>Service Info</h2>
    <pre>${JSON.stringify(info, null, 2)}</pre>
  </body>
</html>`;
}

/**
 * If the server operator provides a `greeting.md` file, it will be included in
 * the rendered info page.
 **/
function buildInfoPageHeader(info: ServiceInfo) {
  const title = getServerTitle();
  // TODO: use some templating engine instead of this mess
  let infoBody = `# ${title}`;

  if (config.staticServiceInfo) {
    return converter.makeHtml(infoBody + customGreeting);
  }

  const waits: string[] = [];

  const allowedModels = MODEL_FAMILIES.filter(isAllowedModel);
  for (const modelFamily of allowedModels) {
    const service = MODEL_FAMILY_SERVICE[modelFamily];

    const hasKeys = keyPool.list().some((k) => {
      return k.service === service && k.modelFamilies.includes(modelFamily);
    });

    const wait = info[modelFamily]?.estimatedQueueTime;
    if (hasKeys && wait) {
      waits.push(`**${MODEL_FAMILY_FRIENDLY_NAME[modelFamily] || modelFamily}**: ${wait}`);
    }
  }

  infoBody += "\n\n" + waits.join(" / ");

  infoBody += customGreeting;

  return converter.makeHtml(infoBody);
}

function getSelfServiceLinks() {
  if (config.gatekeeper !== "user_token") return "";
  const links = [["Check your user token", "/user/lookup"]];

  return `<div class="self-service-links">${links
    .map(([text, link]) => `<a href="${link}">${text}</a>`)
    .join(" | ")}</div>`;
}

export function getServerTitle() {
  return config.serverTitle;
}

export function escapeHtml(unsafe: string) {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#39;")
    .replace(/\[/g, "&#91;")
    .replace(/]/g, "&#93;");
}

function checkIfUnlocked(req: express.Request, res: express.Response, next: express.NextFunction) {
  if (config.serviceInfoPassword?.length && !req.session?.unlocked) {
    return res.redirect("/unlock-info");
  }
  next();
}

const infoPageRouter = Router();
if (config.serviceInfoPassword?.length) {
  infoPageRouter.use(
    express.json({ limit: "1mb" }),
    express.urlencoded({ extended: true, limit: "1mb" })
  );
  infoPageRouter.use(withSession);
  infoPageRouter.use(injectCsrfToken, checkCsrfToken);
  infoPageRouter.post("/unlock-info", (req, res) => {
    if (req.body.password !== config.serviceInfoPassword) {
      return res.status(403).send("Incorrect password");
    }
    req.session!.unlocked = true;
    res.redirect("/");
  });
  infoPageRouter.get("/unlock-info", (_req, res) => {
    if (_req.session?.unlocked) return res.redirect("/");

    res.send(`
      <form method="post" action="/unlock-info">
        <h1>Unlock Service Info</h1>
        <input type="hidden" name="_csrf" value="${res.locals.csrfToken}" />
        <input type="password" name="password" placeholder="Password" />
        <button type="submit">Unlock</button>
      </form>
    `);
  });
  infoPageRouter.use(checkIfUnlocked);
}

infoPageRouter.get("/", handleInfoPage);

infoPageRouter.get("/status", async (req, res) => {
  if (!config.publicJsonInfo) {
    return res.status(404).json({ error: { message: "Not Found" } });
  }

  const baseUrl = getBaseUrl(req);
  return res.json(await buildInfo(baseUrl + config.proxyEndpointRoute, false));
});

infoPageRouter.get("/users", async (_req, res) => {
  if (!config.publicJsonInfo) {
    return res.status(404).json({ error: { message: "Not Found" } });
  }

  return res.json({ users: await getUsersData() });
});

infoPageRouter.get("/public", async (req, res) => {
  if (!config.publicJsonInfo) {
    return res.status(404).json({ error: { message: "Not Found" } });
  }

  const baseUrl = getBaseUrl(req);

  const info = await buildInfo(baseUrl + config.proxyEndpointRoute, false);
  const users = await getUsersData();

  return res.json({ info, users });
});

export { infoPageRouter };
