import type { Database } from "bun:sqlite";
import type { BunSQLiteDatabase } from "drizzle-orm/bun-sqlite";
import type * as DatabaseSchema from "./schema";

import { config } from "@/config";
import { logger } from "@/logger";

export { schema } from "./database";

type DatabaseClient = BunSQLiteDatabase<typeof DatabaseSchema> & {
  $client: Database;
};

let database: DatabaseClient | undefined;
const log = logger.child({ module: "database" });

export function getDatabase() {
  if (!database) throw new Error("Sqlite database not initialized.");
  return database;
}

export async function initializeDatabase() {
  if (typeof database !== "undefined") return;
  log.info("Initializing database SQLite...");

  if (!config.sqliteDataPath) {
    throw new Error("Sqlite data path is undefined. Cannot initialize database.");
  }

  const { drizzle, migrate, schema, logger } = require("./database") as typeof import("./database");
  const path = config.gatekeeperStore === "memory" ? ":memory:" : config.sqliteDataPath;

  database = drizzle(path, { schema, logger });
  try {
    migrate(database, { migrationsFolder: "./drizzle" });
  } catch (error) {
    throw new Error("Error migrating database. Please check logs.", {
      cause: error,
    });
  }

  log.info("Database SQLite initialized.");
}
