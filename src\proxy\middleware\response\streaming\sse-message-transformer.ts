import { Transform, type TransformOptions } from "stream";

import { logger } from "@/logger";
import type { APIFormat } from "@/shared/key-management";
import { assertNever } from "@/shared/utils";

import {
  anthropicChatToOpenAI,
  googleAIToOpenAI,
  type OpenAIChatCompletionStreamEvent,
  passthroughToOpenAI,
  type StreamingCompletionTransformer,
} from "./index";

type SSEMessageTransformerOptions = TransformOptions & {
  requestedModel: string;
  requestId: string;
  inputFormat: APIFormat;
  inputApiVersion?: string;
  outputFormat?: APIFormat;
  logger: typeof logger;
};

/**
 * Transforms SSE messages from one API format to OpenAI chat.completion.chunks.
 * Emits the original string SSE message as an "originalMessage" event.
 */
export class SSEMessageTransformer extends Transform {
  private lastPosition: number;
  private transformState: any;
  private msgCount: number;
  private lastEvent?: string;
  private readonly inputFormat: APIFormat;
  private readonly outputFormat?: APIFormat;
  private readonly transformFn: StreamingCompletionTransformer<OpenAIChatCompletionStreamEvent>;
  private readonly log;
  private readonly fallbackId: string;
  private readonly fallbackModel: string;

  constructor(options: SSEMessageTransformerOptions) {
    super({ ...options, readableObjectMode: true });
    this.log = options.logger?.child({ module: "sse-transformer" });
    this.lastPosition = 0;
    this.msgCount = 0;
    this.transformFn = getTransformer(options.inputFormat);
    this.inputFormat = options.inputFormat;
    this.outputFormat = options.outputFormat;
    this.fallbackId = options.requestId;
    this.fallbackModel = options.requestedModel;
    this.log.debug(
      {
        fn: this.transformFn.name,
        format: options.inputFormat,
        version: options.inputApiVersion,
      },
      "Selected SSE transformer"
    );
  }

  _transform(chunk: Buffer, _encoding: BufferEncoding, callback: Function) {
    try {
      const originalMessage = chunk.toString();
      const {
        event: transformedMessage,
        position: newPosition,
        state,
      } = this.transformFn({
        data: originalMessage,
        lastPosition: this.lastPosition,
        index: this.msgCount++,
        fallbackId: this.fallbackId,
        fallbackModel: this.fallbackModel,
        state: this.transformState,
        lastEvent: this.lastEvent,
      });
      this.lastPosition = newPosition;
      this.transformState = state;

      this.lastEvent = originalMessage;
      this.emit("originalMessage", originalMessage);

      // Some events may not be transformed, e.g. ping events
      if (!transformedMessage) return callback();

      const isLast = transformedMessage?.choices?.[0]?.finish_reason === "stop";
      if (
        this.msgCount === 1 &&
        eventIsOpenAIEvent(transformedMessage) &&
        this.inputFormat !== "openai" &&
        this.outputFormat === "openai"
      ) {
        this.push(createInitialMessage(transformedMessage));
      }

      // OpenAI send usage in the final event. Google AI sends it in every event.
      // But we already fix it in googleAIToOpenAI, so we only need to split
      // the last event into two, with last one only return usage object.
      // This only apply for Google->OpenAI transformer.
      if (this.inputFormat === "google-ai" && this.outputFormat === "openai" && isLast) {
        this.push({ ...transformedMessage, usage: null });
        transformedMessage.choices = [];
      }

      this.push(transformedMessage);
      callback();
    } catch (err) {
      err.lastEvent = chunk?.toString();
      this.log.error(err, "Error transforming SSE message");
      callback(err);
    }
  }
}

function eventIsOpenAIEvent(event: any): event is OpenAIChatCompletionStreamEvent {
  return event?.object === "chat.completion.chunk";
}

function getTransformer(
  responseApi: APIFormat
): StreamingCompletionTransformer<OpenAIChatCompletionStreamEvent> {
  switch (responseApi) {
    case "openai":
    case "openai-response":
      return passthroughToOpenAI;
    case "anthropic-chat":
      return anthropicChatToOpenAI;
    case "google-ai":
      return googleAIToOpenAI;
    default:
      assertNever(responseApi);
  }
}

/**
 * OpenAI streaming chat completions start with an event that contains only the
 * metadata and role (always 'assistant') for the response.  To simulate this
 * for APIs where the first event contains actual content, we create a fake
 * initial event with no content but correct metadata.
 */
function createInitialMessage(
  event: OpenAIChatCompletionStreamEvent
): OpenAIChatCompletionStreamEvent {
  const choices = event.choices ?? [];

  return {
    ...event,
    usage: null,
    choices: choices.map((choice) => ({
      ...choice,
      delta: { role: "assistant", content: "" },
      finish_reason: null,
    })),
  };
}
