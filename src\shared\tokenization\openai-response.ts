import o200k_base from "tiktoken/encoders/o200k_base.json";
import { Tiktoken } from "tiktoken/lite";

import { logger } from "@/logger";

import type { OpenAIResponseInput } from "../api-schemas";
import { libSharp } from "../file-storage";

const log = logger.child({ module: "tokenizer", service: "openai-response" });
const GPT4_VISION_SYSTEM_PROMPT_SIZE = 170;

let encoder: Tiktoken;

export function init() {
  encoder = new Tiktoken(o200k_base.bpe_ranks, o200k_base.special_tokens, o200k_base.pat_str);
  return true;
}

export async function getTokenCount(input: OpenAIResponseInput, model: string) {
  if (typeof input === "string") {
    return getTextTokenCount(input);
  }

  const vision = model.includes("vision") || model.includes("o1");
  let numTokens = vision ? GPT4_VISION_SYSTEM_PROMPT_SIZE : 0;

  for (const message of input) {
    // Base tokens per message
    numTokens += 3; // tokens per message overhead

    if (typeof message.content === "string") {
      numTokens += encoder.encode(message.content).length;
    } else if (Array.isArray(message.content)) {
      for (const contentItem of message.content) {
        if (contentItem.type === "input_text") {
          numTokens += encoder.encode(contentItem.text).length;
        } else if (contentItem.type === "input_image") {
          const cost = await getImageTokenCost(contentItem.image_url, contentItem.detail);
          numTokens += cost ?? 0;
        } else if (contentItem.type === "input_file") {
          // For file content, we estimate based on filename for now
          // In a real implementation, you might want to read the file content
          numTokens += encoder.encode(contentItem.filename).length;
          log.warn(
            { filename: contentItem.filename, file_url: contentItem.file_url },
            "File content tokenization not fully implemented, using filename as estimate"
          );
        }
      }
    }

    // Add tokens for role
    numTokens += encoder.encode(message.role).length;

    if (numTokens > 200000) {
      throw new Error("Content is too large to tokenize.");
    }
  }

  numTokens += 3; // every reply is primed with <|start|>assistant<|message|>
  return { tokenizer: "tiktoken", token_count: numTokens };
}

async function getImageTokenCost(url: string, detail: "auto" | "low" | "high" = "auto") {
  // For now we do not allow remote images as the proxy would have to download
  // them, which is a potential DoS vector.
  if (!url.startsWith("data:image/")) {
    throw new Error(
      "Remote images are not supported. Add the image to your prompt as a base64 data URL."
    );
  }

  const base64Data = url.split(",")[1];
  const buffer = Buffer.from(base64Data, "base64");
  const image = libSharp(buffer);
  const metadata = await image.metadata();

  if (!metadata || !metadata.width || !metadata.height) {
    throw new Error("Prompt includes an image that could not be parsed");
  }

  const { width, height } = metadata;

  let selectedDetail: "low" | "high";
  if (detail === "auto") {
    const threshold = 512 * 512;
    const imageSize = width * height;
    selectedDetail = imageSize > threshold ? "high" : "low";
  } else {
    selectedDetail = detail;
  }

  // https://platform.openai.com/docs/guides/vision/calculating-costs
  if (selectedDetail === "low") {
    log.info(
      { width, height, tokens: 85 },
      "Using fixed GPT-4-Vision token cost for low detail image"
    );
    return 85;
  }

  let newWidth = width;
  let newHeight = height;
  if (width > 2048 || height > 2048) {
    const aspectRatio = width / height;
    if (width > height) {
      newWidth = 2048;
      newHeight = Math.round(2048 / aspectRatio);
    } else {
      newHeight = 2048;
      newWidth = Math.round(2048 * aspectRatio);
    }
  }

  if (newWidth < newHeight) {
    newHeight = Math.round((newHeight / newWidth) * 768);
    newWidth = 768;
  } else {
    newWidth = Math.round((newWidth / newHeight) * 768);
    newHeight = 768;
  }

  const tiles = Math.ceil(newWidth / 512) * Math.ceil(newHeight / 512);
  const tokens = 170 * tiles + 85;

  log.info(
    { width, height, newWidth, newHeight, tiles, tokens },
    "Calculated GPT-4-Vision token cost for high detail image"
  );
  return tokens;
}

function getTextTokenCount(input: string) {
  if (input.length > 500000) {
    return {
      tokenizer: "length fallback",
      token_count: 100000,
    };
  }

  return {
    tokenizer: "tiktoken",
    token_count: encoder.encode(input).length,
  };
}
