import express, { Router } from "express";
import { createWhitelistMiddleware } from "../shared/cidr";
import { HttpError } from "../shared/errors";
import { injectCsrfToken, checkCsrfToken } from "../shared/inject-csrf";
import { injectLocals } from "../shared/inject-locals";
import { withSession } from "../shared/with-session";
import { config } from "../config";
import { renderPage } from "../info-page";
import { buildInfo } from "../service-info";
import { apiLimiter, handleAPIAuthenticate, handleWebAuthenticate } from "./auth";
import { loginRouter } from "./login";
import { eventsApiRouter } from "./api/events";
import { usersApiRouter } from "./api/users";
import { keysApiRouter } from "./api/keys";
import { usersWebRouter as webRouter } from "./web/manage";
import { logger } from "../logger";

const adminRouter = Router();

const whitelist = createWhitelistMiddleware("ADMIN_WHITELIST", config.adminWhitelist);

if (!whitelist.ranges.length && config.adminKey?.length) {
  logger.error(
    "ADMIN_WHITELIST is empty. No admin requests will be allowed. Set 0.0.0.0/0 to allow all."
  );
}

adminRouter.use(whitelist);
adminRouter.use(
  express.json({ limit: "20mb" }),
  express.urlencoded({ extended: true, limit: "20mb" })
);
adminRouter.use(withSession);
adminRouter.use(injectCsrfToken);

adminRouter.use("/api/*", apiLimiter, handleAPIAuthenticate);

adminRouter.use("/api/users", usersApiRouter);
adminRouter.use("/api/keys", keysApiRouter);
adminRouter.use("/api/events", eventsApiRouter);

adminRouter.use(checkCsrfToken);
adminRouter.use(injectLocals);
adminRouter.use("/", loginRouter);
adminRouter.use("/manage", handleWebAuthenticate, webRouter);
adminRouter.use("/service-info", handleWebAuthenticate, async (req, res) => {
  const baseUrl = req.protocol + "://" + req.get("host");

  return res.send(renderPage(await buildInfo(baseUrl + config.proxyEndpointRoute, true)));
});

adminRouter.use(
  (err: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
    const data: Record<string, unknown> = { message: err.message, stack: err.stack };
    if (err instanceof HttpError) {
      data.status = err.status;
      return res.status(err.status).json({ error: data });
    }

    if (err.message === "invalid csrf token") {
      res.clearCookie("csrf");
      req.session.csrf = undefined;

      return res.redirect("/admin/login?redirect=" + req.originalUrl);
    }

    if (err.name === "ForbiddenError") {
      data.status = 403;
      data.message = "Forbidden: " + err.message;

      return res.status(403).json({ error: data });
    }

    return res.status(500).json({ error: data });
  }
);

export { adminRouter };
