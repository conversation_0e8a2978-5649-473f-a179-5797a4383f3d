import { ForbiddenError } from "@/shared/errors";
import { getModelFamilyForRequest, isAllowedModel } from "@/shared/models";

import type { RequestPreprocessor } from "../index";

/**
 * Ensures the selected model family is enabled by the proxy configuration.
 */
export const validateModelFamily: RequestPreprocessor = (req) => {
  const family = getModelFamilyForRequest(req);

  if (!isAllowedModel(family)) {
    throw new ForbiddenError(
      `Model family '${family}' is not enabled on this proxy`
    );
  }
};
