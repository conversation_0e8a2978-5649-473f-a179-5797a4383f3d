import KeyvRedis from "@keyv/redis";
import { createCache } from "cache-manager";
import { Keyv } from "keyv";

import { config } from "@/config";
import { redisClient } from "@/shared/with-session";

export const cacheStore = createCache({
  ttl: 1000 * 60,
  stores: [
    new Keyv({
      // @ts-expect-error: Incorrect type error by @keyv/redis
      store: new KeyvRedis(redisClient),
      namespace: `${config.enviroment}:cache`,
      useKeyPrefix: false,
    }),
  ],
});
