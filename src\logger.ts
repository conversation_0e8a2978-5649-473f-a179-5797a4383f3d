import fs from "fs";
import path from "path";
import pino from "pino";
import pretty from "pino-pretty";

import { config } from "./config";

// Ensure log directory exists if file logging is enabled
function ensureLogDirectory() {
  if (config.logging.output === "file" || config.logging.output === "both") {
    if (!fs.existsSync(config.logging.directory)) {
      fs.mkdirSync(config.logging.directory, { recursive: true });
    }
  }
}

// Create date-based filename
function getLogFileName(type: "default" | "error"): string {
  const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD format
  return path.join(config.logging.directory, `${type}-${today}.log`);
}

// Create logger streams based on configuration
function createLoggerStreams() {
  const streams: pino.StreamEntry[] = [];

  // Console stream
  if (config.logging.output === "console" || config.logging.output === "both") {
    streams.push({
      level: config.logging.level,
      stream: pretty({
        singleLine: true,
        messageFormat: "{if module}\x1b[90m[{module}] \x1b[39m{end}{msg}",
        ignore: "module",
        colorize: true,
      }),
    });
  }

  // File streams
  if (config.logging.output === "file" || config.logging.output === "both") {
    ensureLogDirectory();

    // Default log file (all levels: trace, debug, info, warn, error)
    streams.push({
      level: "trace",
      stream: pino.destination({
        dest: getLogFileName("default"),
        sync: false,
      }),
    });

    // Error log file (error, fatal only)
    streams.push({
      level: "error",
      stream: pino.destination({
        dest: getLogFileName("error"),
        sync: false,
      }),
    });
  }

  return streams;
}

export const logger = pino(
  { level: "trace", base: { pid: process.pid, module: "server" } },
  pino.multistream(createLoggerStreams())
);
