<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "Admin Login" }) %>
  </head>

  <body class="bg-background flex min-h-svh items-center justify-center p-4 text-white">
    <div x-data="login" class="w-full max-w-md">
      <div class="card p-4">
        <h1 class="text-center text-2xl font-bold">Admin <PERSON>gin</h1>

        <form @submit.prevent="handleSubmit">
          <!-- Username field -->
          <div class="mb-6">
            <label for="username" class="text-accent mb-2 block font-bold">Username</label>
            <input
              type="text"
              id="username"
              x-model="username"
              placeholder="Enter your username"
              required
              class="bg-background-tertiary border-border focus:border-accent w-full rounded-lg border px-4 py-2 text-white transition-colors focus:outline-none"
            />
          </div>

          <!-- Password field -->
          <div class="mb-6">
            <label for="password" class="text-accent mb-2 block font-bold">Password</label>
            <input
              type="password"
              id="password"
              x-model="password"
              placeholder="Enter your password"
              required
              class="bg-background-tertiary border-border focus:border-accent w-full rounded-lg border px-4 py-2 text-white transition-colors focus:outline-none"
              @keyup.enter="handleSubmit"
            />
          </div>

          <!-- Error message -->
          <div
            x-show="error"
            x-text="'Error: ' + error"
            class="mb-6 rounded-lg border border-[rgba(244,67,54,0.3)] bg-[rgba(244,67,54,0.1)] p-4 text-[#ff8a80]"
          ></div>

          <!-- Submit button -->
          <div class="flex justify-center">
            <button
              type="submit"
              :disabled="loading"
              class="primary-button flex w-full items-center justify-center"
            >
              <span x-show="loading" class="mr-2">
                <svg
                  class="h-5 w-5 animate-spin text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>

                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </span>

              <span x-text="loading ? 'Logging in...' : 'Login'"></span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <script>
      document.addEventListener("alpine:init", () => {
        Alpine.data("login", () => ({
          username: "",
          password: "",
          loading: false,
          error: null,

          async handleSubmit() {
            this.loading = true;
            this.error = null;

            try {
              const response = await fetch("/admin/login", {
                method: "POST",
                headers: { "Content-Type": "application/x-www-form-urlencoded" },
                body: new URLSearchParams({
                  _csrf: "<%= csrfToken %>",
                  redirect: "<%= redirect %>",
                  username: this.username,
                  password: this.password,
                }),
                credentials: "include",
              });

              const data = await response.json();

              if (response.ok && data.success) {
                window.location.href = data.redirectUrl;
              } else {
                this.error = data.error?.message || "Login failed. Please try again.";
              }
            } catch (error) {
              console.error("Error:", error);
              this.error = "An error occurred. Please try again later.";
            } finally {
              this.loading = false;
            }
          },
        }));
      });
    </script>
  </body>
</html>
