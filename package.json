{"name": "ai-reverse-proxy", "version": "1.0.0", "description": "Reverse proxy for the AI services API", "scripts": {"postinstall": "patch-package", "dev": "bun --watch --no-clear-screen src/server.ts", "start": "NODE_ENV=production bun src/server.ts", "start:debug": "NODE_ENV=production bun --inspect src/server.ts", "build:css": "bunx tailwindcss -i ./src/input.css -o ./public/css/output.css --watch", "build:css:prod": "bunx tailwindcss -i ./src/input.css -o ./public/css/output.css --minify", "typecheck": "tsgo --noEmit", "db:migrate": "drizzle-kit migrate", "db:generate": "drizzle-kit generate", "db:studio": "drizzle-kit studio --verbose", "db:push": "drizzle-kit push --strict --verbose"}, "engines": {"node": ">=22.0.0"}, "bin": {"tailwindcss": "node_modules/@tailwindcss/cli/dist/index.js"}, "author": "", "license": "MIT", "dependencies": {"@anthropic-ai/tokenizer": "^0.0.4", "@keyv/redis": "^5.0.0", "@libsql/client": "^0.15.10", "axios": "^1.11.0", "cache-manager": "^7.1.0", "chalk": "^5.4.1", "chokidar": "^4.0.3", "connect-redis": "^9.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csrf-csrf": "^4.0.3", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.4", "drizzle-zod": "^0.8.2", "ejs": "^3.1.10", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "http-proxy": "1.18.1", "http-proxy-middleware": "^3.0.5", "ipaddr.js": "^2.2.0", "memorystore": "^1.6.7", "microdiff": "^1.5.0", "multer": "^2.0.2", "node-schedule": "^2.1.1", "patch-package": "^8.0.0", "pino": "^9.7.0", "pino-http": "^10.5.0", "proxy-agent": "^6.5.0", "redis": "^5.6.1", "sanitize-html": "^2.17.0", "sharp": "^0.34.3", "showdown": "^2.1.0", "superjson": "^2.2.2", "tiktoken": "^1.0.21", "ulid": "^3.0.1", "uuid": "^11.1.0", "zlib": "^1.0.5", "zod": "^4.0.14"}, "devDependencies": {"@tailwindcss/cli": "^4.1.11", "@types/bun": "^1.2.19", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^4.17.17", "@types/express-session": "^1.18.2", "@types/multer": "^2.0.0", "@types/node": "^24.1.0", "@types/node-schedule": "^2.1.8", "@types/sanitize-html": "^2.16.0", "@types/showdown": "^2.0.6", "@types/uuid": "^10.0.0", "@typescript/native-preview": "^7.0.0-dev.20250731.1", "drizzle-kit": "^0.31.4", "pino-pretty": "^13.1.1", "prettier": "^3.6.2", "prettier-plugin-ejs": "^1.0.3", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "typescript": "^5.9.0-beta"}, "overrides": {"@types/express-serve-static-core": "4.17.33"}, "packageManager": "bun@1.2.19", "trustedDependencies": ["@parcel/watcher", "@tailwindcss/oxide", "esbuild", "sharp"]}