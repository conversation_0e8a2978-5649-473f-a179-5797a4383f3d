import { Router, type RequestH<PERSON><PERSON> } from "express";

import { cacheStore } from "@/shared/cache";
import { keyPool } from "@/shared/key-management";
import { isAllowedModel } from "@/shared/models";
import { createModelList } from "@/shared/utils";

import { addKey, createPreprocessorMiddleware, finalizeBody } from "../middleware/request";
import { createQueuedProxyMiddleware } from "../middleware/request/proxy-middleware-factory";
import type { ProxyResHandlerWithBody } from "../middleware/response";
import { ipLimiter } from "../rate-limit";

export function getModelsResponse() {
  const provider = keyPool.getKeyProvider("groq");
  if (provider.available() === 0) return [];

  // Only return the two allowed models
  const allowedModels = [
    "deepseek-r1-distill-llama-70b",
    "moonshotai/kimi-k2-instruct",
    "qwen/qwen3-32b",
  ];

  return createModelList(allowedModels, "groq", (id) => isAllowedModel(id));
}

const handleModelRequest: RequestHandler = async (_req, res) => {
  const cache = await cacheStore.get<ReturnType<typeof getModelsResponse>>("groq");

  if (cache) {
    res.setHeader("Cache-State", "HIT");
    return res.status(200).json({ object: "list", data: cache });
  }

  const models = getModelsResponse();
  await cacheStore.set("groq", models);

  res.setHeader("Cache-State", "MISS");
  return res.status(200).json({ object: "list", data: models });
};

const groqResponseHandler: ProxyResHandlerWithBody = async (_proxyRes, req, res, body) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  let newBody = body;
  res.status(200).json({ ...newBody, proxy: body.proxy });
};

const groqProxy = createQueuedProxyMiddleware({
  mutations: [addKey, finalizeBody],
  target: "https://api.groq.com/openai",
  blockingResponseHandler: groqResponseHandler,
});

const groqRouter = Router();
groqRouter.get("/v1/models", handleModelRequest);

// General chat completion endpoint
groqRouter.post(
  "/v1/chat/completions",
  ipLimiter,
  createPreprocessorMiddleware({ inboundApi: "openai", outboundApi: "openai", service: "groq" }),
  groqProxy
);

// Redirect browser requests to the homepage.
groqRouter.get("*", (req, res, next) => {
  const isBrowser = req.headers["user-agent"]?.includes("Mozilla");
  if (isBrowser) res.redirect("/");
  else next();
});

export const groq = groqRouter;
