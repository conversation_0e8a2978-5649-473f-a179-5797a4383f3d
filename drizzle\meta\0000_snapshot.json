{"version": "6", "dialect": "sqlite", "id": "9efddca5-d858-4780-9bcc-5cc0ee54c669", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"user-events": {"name": "user-events", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "payload": {"name": "payload", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"idx_user-event": {"name": "idx_user-event", "columns": ["userToken", "type"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}