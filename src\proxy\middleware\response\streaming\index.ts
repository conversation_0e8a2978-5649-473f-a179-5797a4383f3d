export type SSEResponseTransformArgs<S = Record<string, any>> = {
  data: string;
  lastPosition: number;
  index: number;
  fallbackId: string;
  fallbackModel: string;
  state?: S;
  lastEvent?: string;
};

export type AnthropicV2StreamEvent = {
  log_id?: string;
  model?: string;
  completion: string;
  stop_reason: string | null;
};

export type OpenAIChatCompletionStreamEvent = {
  id: string;
  object: "chat.completion.chunk";
  created: number;
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    completion_tokens_details: { reasoning_tokens: number };
  } | null;
  choices: {
    index: number;
    delta: { role?: string; content?: string };
    finish_reason: string | null;
  }[];
};

export type GoogleAIStreamEvent = {
  modelVersion: string;
  responseId: string;
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
    promptTokensDetails: { modality: string; tokenCount: number }[];
    thoughtsTokenCount: number;
  };
  candidates: {
    content: { parts: { text: string; thought?: boolean }[]; role: string };
    finishReason?: "STOP" | "MAX_TOKENS" | "SAFETY" | "RECITATION" | "OTHER";
    index: number;
    tokenCount: number;
    safetyRatings: { category: string; probability: string }[];
  }[];
};

export type StreamingCompletionTransformer<T = OpenAIChatCompletionStreamEvent, S = any> = (
  params: SSEResponseTransformArgs<S>
) => {
  position: number;
  event?: T;
  state?: S;
};

export { mergeEventsForAnthropicChat } from "./aggregators/anthropic-chat";
export { mergeEventsForGoogleAI } from "./aggregators/google-ai";
export { mergeEventsForOpenAIChat } from "./aggregators/openai-chat";
export { mergeEventsForOpenAIResponse } from "./aggregators/openai-response";

export { anthropicChatToOpenAI } from "./transformers/anthropic-chat-to-openai";
export { googleAIToOpenAI } from "./transformers/google-ai-to-openai";
export { passthroughToOpenAI } from "./transformers/passthrough-to-openai";
