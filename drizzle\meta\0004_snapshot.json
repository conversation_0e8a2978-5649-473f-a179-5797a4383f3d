{"version": "6", "dialect": "sqlite", "id": "2fec7925-d6a5-4b2e-ac03-066f72f23cd3", "prevId": "cbaeb2ea-4405-4b6c-beb2-04d8309bbe2d", "tables": {"users-events": {"name": "users-events", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "payload": {"name": "payload", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"idx_user-event": {"name": "idx_user-event", "columns": ["userToken", "type"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"token": {"name": "token", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "nickname": {"name": "nickname", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "adminNote": {"name": "adminNote", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "maxIps": {"name": "maxIps", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'normal'"}, "meta": {"name": "meta", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'{\"private\":{},\"public\":{}}'"}, "isDisabled": {"name": "isDisabled", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "disabledAt": {"name": "disabledAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "disabledReason": {"name": "disabledReason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "lastUsedAt": {"name": "lastUsedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"idx_user": {"name": "idx_user", "columns": ["token"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users-ip": {"name": "users-ip", "columns": {"userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "lastUsedAt": {"name": "lastUsedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"user_ips_user_ip_unique": {"name": "user_ips_user_ip_unique", "columns": ["userToken", "ip"], "isUnique": true}}, "foreignKeys": {"users-ip_userToken_users_token_fk": {"name": "users-ip_userToken_users_token_fk", "tableFrom": "users-ip", "tableTo": "users", "columnsFrom": ["userToken"], "columnsTo": ["token"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users-ip-usage": {"name": "users-ip-usage", "columns": {"userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelFamily": {"name": "modelFamily", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelId": {"name": "modelId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "inputTokens": {"name": "inputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputTokens": {"name": "outputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "prompts": {"name": "prompts", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "inputTokensSinceStart": {"name": "inputTokensSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputTokensSinceStart": {"name": "outputTokensSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "promptsSinceStart": {"name": "promptsSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "lastUsedAt": {"name": "lastUsedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"user_ip_usage_user_ip_model_id_unique": {"name": "user_ip_usage_user_ip_model_id_unique", "columns": ["ip", "modelId", "userToken", "modelFamily"], "isUnique": true}}, "foreignKeys": {"users-ip-usage_userToken_users_token_fk": {"name": "users-ip-usage_userToken_users_token_fk", "tableFrom": "users-ip-usage", "tableTo": "users", "columnsFrom": ["userToken"], "columnsTo": ["token"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users-usage": {"name": "users-usage", "columns": {"userToken": {"name": "userToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelFamily": {"name": "modelFamily", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelId": {"name": "modelId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "inputTokens": {"name": "inputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputTokens": {"name": "outputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "prompts": {"name": "prompts", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "inputTokensSinceStart": {"name": "inputTokensSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputTokensSinceStart": {"name": "outputTokensSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "promptsSinceStart": {"name": "promptsSinceStart", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "lastUsedAt": {"name": "lastUsedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"user_usage_user_model_unique": {"name": "user_usage_user_model_unique", "columns": ["userToken", "modelId", "modelFamily"], "isUnique": true}}, "foreignKeys": {"users-usage_userToken_users_token_fk": {"name": "users-usage_userToken_users_token_fk", "tableFrom": "users-usage", "tableTo": "users", "columnsFrom": ["userToken"], "columnsTo": ["token"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"users-usage_userToken_modelId_modelFamily_pk": {"columns": ["userToken", "modelId", "modelFamily"], "name": "users-usage_userToken_modelId_modelFamily_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}