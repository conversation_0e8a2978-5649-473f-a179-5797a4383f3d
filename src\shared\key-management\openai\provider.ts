import crypto from "crypto";
import http from "http";

import { config } from "@/config";
import { logger } from "@/logger";

import { PaymentRequiredError } from "@/shared/errors";
import { getOpenAIModelFamily, type OpenAIModelFamily } from "@/shared/models";
import { getTokenCostUsd } from "@/shared/stats";

import { defaultUsages, type Key, type KeyProvider, type Usages } from "..";
import { prioritizeKeys } from "../prioritize-keys";
import { OpenAIKeyChecker } from "./checker";

type OpenAIKeyUsage = {
  [K in OpenAIModelFamily as `${K}-usages`]: Usages;
};

export interface OpenAIKey extends Key, OpenAIKeyUsage {
  readonly service: "openai";
  modelFamilies: OpenAIModelFamily[];
  /**
   * Some keys are assigned to multiple organizations, each with their own quota
   * limits. We clone the key for each organization and track usage/disabled
   * status separately.
   */
  organizationId?: string;
  /** Whether this is a free trial key. These are prioritized over paid keys if they can fulfill the request. */
  tier: 1 | 2 | 3 | 4 | 5 | "unknown";
  /** Whether this key org have been verified. Used to access o3 and others models */
  isVerified: boolean;
  /** Set when key check returns a non-transient 429. */
  isOverQuota: boolean;
  /**
   * Last known X-RateLimit-Requests-Reset header from OpenAI, converted to a
   * number.
   * Formatted as a `\d+(m|s)` string denoting the time until the limit resets.
   * Specifically, it seems to indicate the time until the key's quota will be
   * fully restored; the key may be usable before this time as the limit is a
   * rolling window.
   *
   * Requests which return a 429 do not count against the quota.
   *
   * Requests which fail for other reasons (e.g. 401) count against the quota.
   */
  rateLimitRequestsReset: number;
  /**
   * Last known X-RateLimit-Tokens-Reset header from OpenAI, converted to a
   * number.
   * Appears to follow the same format as `rateLimitRequestsReset`.
   *
   * Requests which fail do not count against the quota as they do not consume
   * tokens.
   */
  rateLimitTokensReset: number;
  /**
   * Model snapshots available.
   */
  modelIds: string[];
}

export type OpenAIKeyUpdate = Omit<Partial<OpenAIKey>, "key" | "hash" | "prompts">;

/**
 * Upon assigning a key, we will wait this many milliseconds before allowing it
 * to be used again. This is to prevent the queue from flooding a key with too
 * many requests while we wait to learn whether previous ones succeeded.
 */
const KEY_REUSE_DELAY = 1000;

export class OpenAIKeyProvider implements KeyProvider<OpenAIKey> {
  readonly service = "openai" as const;

  private keys: OpenAIKey[] = [];
  private checker?: OpenAIKeyChecker;
  private log = logger.child({ module: "key-provider", service: this.service });

  constructor() {
    const keyConfig = config.keys.openai;

    if (keyConfig.length === 0) {
      this.log.warn("OPENAI_KEYS is not set. OpenAI API will not be available.");
      return;
    }

    for (const key of keyConfig) this.addKey({ key, isStartup: true });
    this.log.info({ keyCount: this.keys.length }, "Loaded OpenAI keys.");
  }

  public addKey({ key, isStartup = false }: { key: string; isStartup?: boolean }): boolean {
    const hash = `oai-${crypto.createHash("sha256").update(key).digest("hex").slice(0, 8)}`;

    if (!isStartup) {
      const isExist = this.keys.find((k) => k.hash === hash);
      if (isExist) return false;
    }

    const newKey: OpenAIKey = {
      key: key,
      hash: hash,
      service: "openai" as const,
      modelFamilies: ["turbo", "gpt4", "gpt4-turbo", "gpt4o"],
      tier: "unknown",
      isDisabled: false,
      isRevoked: false,
      isOverQuota: false,
      isVerified: false,
      disabledReason: undefined,
      disabledAt: undefined,
      disabledBy: undefined,
      input: { tokens: 0, cost: 0 },
      output: { tokens: 0, cost: 0 },
      lastUsedAt: 0,
      lastCheckedAt: 0,
      firstCheckedAt: 0,
      prompts: 0,
      rateLimitedAt: 0,
      rateLimitedUntil: 0,
      rateLimitRequestsReset: 0,
      rateLimitTokensReset: 0,
      modelIds: [],
      addedAt: Date.now(),

      "turbo-usages": structuredClone(defaultUsages),
      "gpt4-usages": structuredClone(defaultUsages),
      "gpt4-turbo-usages": structuredClone(defaultUsages),

      "gpt4o-usages": structuredClone(defaultUsages),
      "gpt4o-mini-usages": structuredClone(defaultUsages),
      "chatgpt-4o-usages": structuredClone(defaultUsages),
      "codex-mini-usages": structuredClone(defaultUsages),

      "gpt4.5-usages": structuredClone(defaultUsages),
      "gpt4.1-usages": structuredClone(defaultUsages),
      "gpt4.1-mini-usages": structuredClone(defaultUsages),
      "gpt4.1-nano-usages": structuredClone(defaultUsages),

      "o1-pro-usages": structuredClone(defaultUsages),
      "o1-usages": structuredClone(defaultUsages),
      "o1-mini-usages": structuredClone(defaultUsages),
      "o3-pro-usages": structuredClone(defaultUsages),
      "o3-usages": structuredClone(defaultUsages),
      "o3-mini-usages": structuredClone(defaultUsages),
      "o4-mini-usages": structuredClone(defaultUsages),
    };
    this.keys.push(newKey);

    return true;
  }

  public removeKey(hash: string): boolean {
    const keyIndex = this.keys.findIndex((k) => k.hash === hash);
    if (keyIndex === -1) return false;

    this.keys.splice(keyIndex, 1);
    return true;
  }

  public init() {
    if (config.checkKeys) {
      const cloneFn = this.clone.bind(this);
      const updateFn = this.update.bind(this);
      const disableFn = this.disable.bind(this);
      this.checker = new OpenAIKeyChecker(this.keys, cloneFn, updateFn, disableFn);
      this.checker.start();
    }
  }

  /**
   * Returns a list of all keys, with the key field removed.
   * Don't mutate returned keys, use a KeyPool method instead.
   **/
  public list() {
    return this.keys.map((key) => Object.freeze({ ...key, key: undefined }));
  }

  public get(model: string): OpenAIKey {
    this.log.debug({ model }, "Selecting key");
    const neededFamily = getOpenAIModelFamily(model);
    const excludeTrials = model === "text-embedding-ada-002";

    const availableKeys = this.keys.filter(
      (key) =>
        !key.isDisabled &&
        !key.isRevoked &&
        !key.isOverQuota &&
        key.modelFamilies.includes(neededFamily) &&
        (!excludeTrials || key.tier !== 1)
    );

    if (availableKeys.length === 0) {
      throw new PaymentRequiredError(`No OpenAI keys available for model ${model}`);
    }

    const keysByPriority = prioritizeKeys(availableKeys, (a, b) => {
      if (a.tier === b.tier) return 0;
      if (a.tier === "unknown") return 1;
      if (b.tier === "unknown") return -1;
      return a.tier - b.tier;
    });

    const selectedKey = keysByPriority[0];
    selectedKey.lastUsedAt = Date.now();
    this.throttle(selectedKey.hash);
    return { ...selectedKey };
  }

  /** Called by the key checker to update key information. */
  public update(keyHash: string, update: OpenAIKeyUpdate) {
    const keyFromPool = this.keys.find((k) => k.hash === keyHash)!;
    Object.assign(keyFromPool, { lastCheckedAt: Date.now(), ...update });
  }

  /** Called by the key checker to create clones of keys for the given orgs. */
  public clone(keyHash: string, newOrgIds: string[]) {
    const keyFromPool = structuredClone(this.keys.find((k) => k.hash === keyHash)!);

    const clones = newOrgIds.map((orgId) => {
      const clone: OpenAIKey = {
        ...keyFromPool,
        organizationId: orgId,
        isDisabled: false,
        isRevoked: false,
        isOverQuota: false,
        isVerified: false,
        hash: `oai-${crypto
          .createHash("sha256")
          .update(keyFromPool.key + orgId)
          .digest("hex")
          .slice(0, 8)}`,
        lastCheckedAt: 0, // Force re-check in case the org has different models
      };
      this.log.info(
        { cloneHash: clone.hash, parentHash: keyFromPool.hash, orgId },
        "Cloned organization key"
      );
      return clone;
    });

    this.keys.push(...clones);
  }

  /** Disables a key, or does nothing if the key isn't in this pool. */
  public disable(key: Key, reason?: "quota" | "revoked" | "error", disabledBy?: string) {
    const keyFromPool = this.keys.find((k) => k.hash === key.hash);
    if (!keyFromPool || keyFromPool.isDisabled) return;

    keyFromPool.isDisabled = true;
    keyFromPool.disabledReason = reason;
    keyFromPool.disabledAt = Date.now();
    keyFromPool.disabledBy = disabledBy || "system";

    this.log.warn(
      {
        key: keyFromPool.hash,
        reason: keyFromPool.disabledReason,
        disabledBy: keyFromPool.disabledBy,
      },
      "Key disabled"
    );
  }

  public available() {
    return this.keys.filter((k) => !k.isDisabled).length;
  }

  /**
   * Given a model, returns the period until a key will be available to service
   * the request, or returns 0 if a key is ready immediately.
   */
  public getLockoutPeriod(family: OpenAIModelFamily): number {
    // TODO: this is really inefficient on servers with large key pools and we
    // are calling it every 50ms, per model family.

    const activeKeys = this.keys.filter(
      (key) => !key.isDisabled && key.modelFamilies.includes(family)
    );

    // Don't lock out if there are no keys available or the queue will stall.
    // Just let it through so the add-key middleware can throw an error.
    if (activeKeys.length === 0) return 0;

    // A key is rate-limited if its `rateLimitedAt` plus the greater of its
    // `rateLimitRequestsReset` and `rateLimitTokensReset` is after the
    // current time.

    // If there are any keys that are not rate-limited, we can fulfill requests.
    const now = Date.now();
    const rateLimitedKeys = activeKeys.filter((key) => {
      const resetTime = Math.max(key.rateLimitRequestsReset, key.rateLimitTokensReset);
      return now < key.rateLimitedAt + Math.min(20000, resetTime);
    }).length;
    const anyNotRateLimited = rateLimitedKeys < activeKeys.length;

    if (anyNotRateLimited) {
      return 0;
    }

    // If all keys are rate-limited, return the time until the first key is
    // ready. We don't want to wait longer than 10 seconds because rate limits
    // are a rolling window and keys may become available sooner than the stated
    // reset time.
    return Math.min(
      ...activeKeys.map((key) => {
        const resetTime = Math.max(key.rateLimitRequestsReset, key.rateLimitTokensReset);
        return key.rateLimitedAt + Math.min(20000, resetTime) - now;
      })
    );
  }

  public markRateLimited(keyHash: string) {
    this.log.debug({ key: keyHash }, "Key rate limited");
    const key = this.keys.find((k) => k.hash === keyHash)!;
    const now = Date.now();
    key.rateLimitedAt = now;

    // Most OpenAI reqeuests will provide a `x-ratelimit-reset-requests` header
    // header telling us when to try again which will be set in a call to
    // `updateRateLimits`.  These values below are fallbacks in case the header
    // is not provided.
    key.rateLimitRequestsReset = 10000;
    key.rateLimitedUntil = now + key.rateLimitRequestsReset;
  }

  public incrementUsage(keyHash: string, model: string, inputTokens: number, outputTokens: number) {
    const key = this.keys.find((k) => k.hash === keyHash);
    if (!key) return;

    const modelFamily = getOpenAIModelFamily(model);
    const cost = getTokenCostUsd({ modelFamily, inputTokens, outputTokens });

    key.prompts++;
    key.input.tokens += inputTokens;
    key.output.tokens += outputTokens;
    key.input.cost += cost.input;
    key.output.cost += cost.output;

    key[`${modelFamily}-usages`].prompts += 1;
    key[`${modelFamily}-usages`].input.tokens += inputTokens;
    key[`${modelFamily}-usages`].output.tokens += outputTokens;
    key[`${modelFamily}-usages`].input.cost += cost.input;
    key[`${modelFamily}-usages`].output.cost += cost.output;
  }

  public updateRateLimits(keyHash: string, headers: http.IncomingHttpHeaders) {
    const key = this.keys.find((k) => k.hash === keyHash)!;
    const requestsReset = headers["x-ratelimit-reset-requests"];
    const tokensReset = headers["x-ratelimit-reset-tokens"];

    if (typeof requestsReset === "string") {
      key.rateLimitRequestsReset = getResetDurationMillis(requestsReset);
    }

    if (typeof tokensReset === "string") {
      key.rateLimitTokensReset = getResetDurationMillis(tokensReset);
    }

    if (!requestsReset && !tokensReset) {
      this.log.warn({ key: key.hash }, `No ratelimit headers; skipping update`);
      return;
    }

    const { rateLimitedAt, rateLimitRequestsReset, rateLimitTokensReset } = key;
    const rateLimitedUntil = rateLimitedAt + Math.max(rateLimitRequestsReset, rateLimitTokensReset);
    if (rateLimitedUntil > Date.now()) {
      key.rateLimitedUntil = rateLimitedUntil;
    }
  }

  public recheck() {
    this.keys.forEach((key) => {
      this.update(key.hash, {
        isOverQuota: false,
        isDisabled: false,
        lastCheckedAt: 0,
      });
    });
    this.checker?.scheduleNextCheck();
  }

  /**
   * Called when a key is selected for a request, briefly disabling it to
   * avoid spamming the API with requests while we wait to learn whether this
   * key is already rate limited.
   */
  private throttle(hash: string) {
    const now = Date.now();
    const key = this.keys.find((k) => k.hash === hash)!;

    const currentRateLimit =
      Math.max(key.rateLimitRequestsReset, key.rateLimitTokensReset) + key.rateLimitedAt;
    const nextRateLimit = now + KEY_REUSE_DELAY;

    // Don't throttle if the key is already naturally rate limited.
    if (currentRateLimit > nextRateLimit) return;

    key.rateLimitedAt = Date.now();
    key.rateLimitRequestsReset = KEY_REUSE_DELAY;
    key.rateLimitedUntil = Date.now() + KEY_REUSE_DELAY;
  }
}

// wip
function calculateRequestsPerMinute(headers: http.IncomingHttpHeaders) {
  const requestsLimit = headers["x-ratelimit-limit-requests"];
  const requestsReset = headers["x-ratelimit-reset-requests"];

  if (typeof requestsLimit !== "string" || typeof requestsReset !== "string") {
    return 0;
  }

  const limit = parseInt(requestsLimit, 10);
  const reset = getResetDurationMillis(requestsReset);

  // If `reset` is less than one minute, OpenAI specifies the `limit` as an
  // integer representing requests per minute.  Otherwise it actually means the
  // requests per day.
  const isPerMinute = reset < 60000;
  if (isPerMinute) return limit;
  return limit / 1440;
}

/**
 * Converts reset string ("14m25s", "21.0032s", "14ms" or "21ms") to a number of
 * milliseconds.
 **/
function getResetDurationMillis(resetDuration?: string): number {
  const match = resetDuration?.match(/(?:(\d+)m(?!s))?(?:(\d+(?:\.\d+)?)s)?(?:(\d+)ms)?/);

  if (match) {
    const [, minutes, seconds, milliseconds] = match.map(Number);

    const minutesToMillis = (minutes || 0) * 60 * 1000;
    const secondsToMillis = (seconds || 0) * 1000;
    const millisecondsValue = milliseconds || 0;

    return minutesToMillis + secondsToMillis + millisecondsValue;
  }

  return 0;
}
