import { relations, sql } from "drizzle-orm";
import { index, int, primaryKey, sqliteTable, text, uniqueIndex } from "drizzle-orm/sqlite-core";

import { v4 } from "uuid";

import { config } from "@/config";
import { MODEL_FAMILIES } from "@/shared/models";

type Meta = Record<"public" | "private", Record<string, unknown>>;

export const users = sqliteTable(
  "users",
  {
    token: text()
      .primaryKey()
      .$defaultFn(() => v4()),

    // User
    nickname: text(),
    adminNote: text(),
    maxIps: int({ mode: "number" })
      .notNull()
      .$defaultFn(() => config.maxIpsPerUser),
    type: text({ enum: ["normal", "special", "temporary"] })
      .notNull()
      .default("normal"),
    meta: text({ mode: "json" }).$type<Meta>().default({ private: {}, public: {} }),

    disAllowedModels: text({ mode: "text" }).notNull().default(`{"json":[]}`),

    // Ban
    isDisabled: int({ mode: "boolean" }),
    disabledAt: int({ mode: "timestamp" }),
    disabledReason: text(),

    createdAt: int({ mode: "timestamp" })
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
    lastUsedAt: int({ mode: "timestamp" })
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
  },
  (table) => [index("idx_user").on(table.token)]
);

export const users_usage = sqliteTable(
  "users-usage",
  {
    userToken: text()
      .notNull()
      .references(() => users.token, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),

    modelFamily: text({ enum: MODEL_FAMILIES }).notNull(),
    modelId: text().notNull(),

    inputTokens: int({ mode: "number" }).notNull().default(0),
    outputTokens: int({ mode: "number" }).notNull().default(0),
    prompts: int({ mode: "number" }).notNull().default(0),

    inputTokensSinceStart: int({ mode: "number" }).notNull().default(0),
    outputTokensSinceStart: int({ mode: "number" }).notNull().default(0),
    promptsSinceStart: int({ mode: "number" }).notNull().default(0),

    createdAt: int({ mode: "timestamp" })
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
    lastUsedAt: int({ mode: "timestamp" })
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
  },
  (table) => [
    primaryKey({
      columns: [table.userToken, table.modelId, table.modelFamily],
    }),
    uniqueIndex("user_usage_user_model_unique").on(
      table.userToken,
      table.modelId,
      table.modelFamily
    ),
  ]
);

export const users_ip = sqliteTable(
  "users-ip",
  {
    userToken: text()
      .notNull()
      .references(() => users.token, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),

    ip: text().notNull(),

    createdAt: int({ mode: "timestamp" })
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
    lastUsedAt: int({ mode: "timestamp" })
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
  },
  (table) => [uniqueIndex("user_ips_user_ip_unique").on(table.userToken, table.ip)]
);

export const users_ip_usage = sqliteTable(
  "users-ip-usage",
  {
    userToken: text()
      .notNull()
      .references(() => users.token, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),

    ip: text().notNull(),
    modelFamily: text({ enum: MODEL_FAMILIES }).notNull(),
    modelId: text().notNull(),

    inputTokens: int({ mode: "number" }).notNull().default(0),
    outputTokens: int({ mode: "number" }).notNull().default(0),
    prompts: int({ mode: "number" }).notNull().default(0),

    inputTokensSinceStart: int({ mode: "number" }).notNull().default(0),
    outputTokensSinceStart: int({ mode: "number" }).notNull().default(0),
    promptsSinceStart: int({ mode: "number" }).notNull().default(0),

    createdAt: int({ mode: "timestamp" })
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
    lastUsedAt: int({ mode: "timestamp" })
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
  },
  (table) => [
    uniqueIndex("user_ip_usage_user_ip_model_id_unique").on(
      table.ip,
      table.modelId,
      table.userToken,
      table.modelFamily
    ),
  ]
);

export const users_limits = sqliteTable("users-limits", {
  userToken: text()
    .notNull()
    .references(() => users.token, { onDelete: "cascade", onUpdate: "cascade" }),

  limitNextReset: int({ mode: "timestamp" }),
  limitReset: text({ mode: "text" })
    .notNull()
    .$type<"none" | "hourly" | "daily" | "weekly" | "monthly">()
    .default("none"),

  modelFamily: text({ enum: MODEL_FAMILIES }),

  promptLimits: int({ mode: "number" }).notNull().default(0),
  promptsUsed: int({ mode: "number" }).notNull().default(0),

  tokenLimits: int({ mode: "number" }).notNull().default(0),
  tokensUsed: int({ mode: "number" }).notNull().default(0),

  createdAt: int({ mode: "timestamp" })
    .default(sql`(strftime('%s', 'now'))`)
    .notNull(),
  updatedAt: int({ mode: "timestamp" })
    .default(sql`(strftime('%s', 'now'))`)
    .notNull(),
});

export const users_relations = relations(users, ({ many }) => ({
  ips: many(users_ip),
  usages: many(users_usage),
  limits: many(users_limits),
}));

export const users_usage_relations = relations(users_usage, ({ one }) => ({
  user: one(users, {
    fields: [users_usage.userToken],
    references: [users.token],
  }),
}));

export const users_ip_relations = relations(users_ip, ({ one, many }) => ({
  user: one(users, { fields: [users_ip.userToken], references: [users.token] }),
  usages: many(users_ip_usage),
}));

export const users_ip_usage_relations = relations(users_ip_usage, ({ one }) => ({
  user: one(users, {
    fields: [users_ip_usage.userToken],
    references: [users.token],
  }),
  user_ip: one(users_ip, {
    fields: [users_ip_usage.ip],
    references: [users_ip.ip],
  }),
}));

export const users_limits_relations = relations(users_limits, ({ one }) => ({
  user: one(users, { fields: [users_limits.userToken], references: [users.token] }),
}));
