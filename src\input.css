@import "tailwindcss";
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono&display=swap");

@theme {
  /* Shared color palette based on admin_view-user.ejs */
  --color-accent: #58739c;
  --color-accent-hover: #4a6384;
  --color-accent-light: #8bc4ff;

  --color-danger: #f44336;
  --color-danger-hover: #d32f2f;

  --color-background: #1a1a1a;
  --color-background-secondary: #252525;
  --color-background-tertiary: #151515;
  --color-text: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.6);
  --color-border: #252525;

  --color-success: #4caf50;
  --color-success-hover: #388e3c;
  --color-border-success: #2e6c2e;
  --color-background-success: #1e301e;

  --color-warning: #ff9800;
  --color-warning-hover: #f57c00;
  --color-border-warning: #6c5b00;
  --color-background-warning: #302e00;

  --color-error: #f44336;
  --color-error-hover: #d32f2f;
  --color-border-error: #6c2924;
  --color-background-error: #301e1d;

  /* Shadow colors */
  --color-shadow: #171717;
  --color-shadow-heavy: rgba(0, 0, 0, 0.5);

  --font-mono:
    "Zed Plex Mono", "JetBrains Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
}

@layer base {
  code {
    @apply bg-background border-border rounded-lg border px-2 py-1;
  }
}

/* Custom CSS classes */
@layer components {
  .card {
    @apply border-border bg-background rounded-lg border px-4 py-2 shadow-lg/50 shadow-black;
  }

  .button {
    @apply border-border bg-background cursor-pointer rounded-lg border px-4 py-2 font-medium text-white;

    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .primary-button {
    @apply border-border bg-accent hover:bg-accent-hover cursor-pointer rounded-lg border px-4 py-2 font-medium text-white;

    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .secondary-button {
    @apply border-border bg-background-tertiary hover:bg-background-secondary cursor-pointer rounded-lg border px-4 py-2 font-medium text-white;

    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .success-button {
    @apply border-border bg-success hover:bg-success-hover cursor-pointer rounded-lg border px-4 py-2 font-medium text-white;

    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .warning-button {
    @apply border-border bg-warning hover:bg-warning-hover cursor-pointer rounded-lg border px-4 py-2 font-medium text-white;

    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .danger-button {
    @apply border-border bg-danger hover:bg-danger-hover cursor-pointer rounded-lg border px-4 py-2 font-medium text-white;

    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .cost {
    @apply text-accent-light text-sm;
  }
}
