import { eq, sql } from "drizzle-orm";
import { Router } from "express";

import { config } from "@/config";
import { getDatabase } from "@/shared/database";
import { schema } from "@/shared/database/database";
import { getTokenCostUsd } from "@/shared/stats";
import { hashIp } from "@/shared/users/user-store";

const router = Router();

router.get("/", (_req, res) => {
  res.redirect("/");
});

router.use(async (req, res, next) => {
  if (req.session.userToken) {
    const data = await getUserInfo(req.session.userToken);

    if (!data) {
      delete req.session.userToken;
      return next();
    }

    res.locals.currentSelfServiceUser = data;
  }

  next();
});

router.get("/lookup", (req, res) => {
  const ipLimit = res.locals.currentSelfServiceUser?.maxIps ?? config.maxIpsPerUser;

  res.render("user_lookup", {
    ipLimit,
    ip: hashIp(req.ip),
    user: res.locals.currentSelfServiceUser,
  });
});

export function truncateToken(token: string) {
  const sliceLength = Math.max(Math.floor(token.length / 8), 1);
  return `${token.slice(0, sliceLength)}...${token.slice(-sliceLength)}`;
}

export async function getUserInfo(token: string) {
  const db = getDatabase();

  const data = await db.query.users.findFirst({
    columns: { adminNote: false },
    where: (fields, { eq }) => eq(fields.token, token),
    with: { ips: true },
  });

  if (!data) return null;

  // @ts-expect-error
  delete data.meta?.private;

  const userUsages = db
    .select({
      modelFamily: schema.users_usage.modelFamily,
      prompts: sql<number>`SUM(${schema.users_usage.prompts})`,
      inputTokens: sql<number>`SUM(${schema.users_usage.inputTokens})`,
      outputTokens: sql<number>`SUM(${schema.users_usage.outputTokens})`,
      promptsSinceStart: sql<number>`SUM(${schema.users_usage.promptsSinceStart})`,
      inputTokensSinceStart: sql<number>`SUM(${schema.users_usage.inputTokensSinceStart})`,
      outputTokensSinceStart: sql<number>`SUM(${schema.users_usage.outputTokensSinceStart})`,
      lastUsedAt: sql<number>`MAX(${schema.users_usage.lastUsedAt})`,
    })
    .from(schema.users_usage)
    .where(eq(schema.users_usage.userToken, token))
    .groupBy(schema.users_usage.modelFamily)
    .orderBy(sql`${schema.users_usage.lastUsedAt} DESC`);

  const usages = (await userUsages).map((usage) => {
    const cost = getTokenCostUsd(usage);
    const costSinceStart = getTokenCostUsd({
      modelFamily: usage.modelFamily,
      inputTokens: usage.inputTokensSinceStart,
      outputTokens: usage.outputTokensSinceStart,
    });

    return {
      modelFamily: usage.modelFamily,
      total: {
        input: { tokens: usage.inputTokens, cost: cost.input },
        output: { tokens: usage.outputTokens, cost: cost.output },
        prompts: usage.prompts,
      },
      sinceStart: {
        input: { tokens: usage.inputTokensSinceStart, cost: costSinceStart.input },
        output: { tokens: usage.outputTokensSinceStart, cost: costSinceStart.output },
        prompts: usage.promptsSinceStart,
      },
    };
  });

  return { ...data, usages, ips: data.ips.length };
}

export { router as selfServiceRouter };
