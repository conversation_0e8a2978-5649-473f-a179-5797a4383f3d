import { Router, type Request, type <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { cacheStore } from "@/shared/cache";
import { keyPool, type <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/shared/key-management";
import { isAllowedModel } from "@/shared/models";
import { createModelList } from "@/shared/utils";

import { addKey, createPreprocessorMiddleware, finalizeBody } from "../middleware/request";
import { createQueuedProxyMiddleware } from "../middleware/request/proxy-middleware-factory";
import { ProxyReqManager } from "../middleware/request/proxy-req-manager";
import type { ProxyResHandlerWithBody } from "../middleware/response";
import { ipLimiter } from "../rate-limit";

export function getModelsResponse() {
  const keyProvider = keyPool.getKeyProvider("anthropic");
  if (keyProvider.available() === 0) return [];

  const keys = keyProvider.list() as Anthropic<PERSON>ey[];
  const modelIds = Array.from(new Set(keys.map((k) => k.modelIds).flat()));

  return createModelList(modelIds, "anthropic", (id) => isAllowedModel(id));
}

const handleModelRequest: RequestHandler = async (_req, res) => {
  const cache = await cacheStore.get<ReturnType<typeof getModelsResponse>>("anthropic");

  if (cache) {
    res.setHeader("Cache-State", "HIT");
    return res.status(200).json({ object: "list", data: cache });
  }

  const models = getModelsResponse();
  await cacheStore.set("anthropic", models);

  res.setHeader("Cache-State", "MISS");
  return res.status(200).json({ object: "list", data: models });
};

const anthropicBlockingResponseHandler: ProxyResHandlerWithBody = async (
  _proxyRes,
  req,
  res,
  body
) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  let newBody = body;
  switch (`${req.inboundApi}<-${req.outboundApi}`) {
    case "openai<-anthropic-chat":
      req.log.info("Transforming Anthropic Chat back to OpenAI format");
      newBody = transformAnthropicChatResponseToOpenAI(body);
      break;
  }

  res.status(200).json({ ...newBody, proxy: body.proxy });
};

function flattenChatResponse(content: { type: string; text: string }[]): string {
  return content
    .map((part: { type: string; text: string }) => (part.type === "text" ? part.text : ""))
    .join("\n");
}

export function transformAnthropicChatResponseToAnthropicText(
  anthropicBody: Record<string, any>
): Record<string, any> {
  return {
    type: "completion",
    id: "ant-" + anthropicBody.id,
    completion: flattenChatResponse(anthropicBody.content),
    stop_reason: anthropicBody.stop_reason,
    stop: anthropicBody.stop_sequence,
    model: anthropicBody.model,
    usage: anthropicBody.usage,
  };
}

function transformAnthropicTextResponseToOpenAI(
  anthropicBody: Record<string, any>,
  req: Request
): Record<string, any> {
  const totalTokens = (req.promptTokens ?? 0) + (req.outputTokens ?? 0);
  return {
    id: "ant-" + anthropicBody.log_id,
    object: "chat.completion",
    created: Date.now(),
    model: anthropicBody.model,
    usage: {
      prompt_tokens: req.promptTokens,
      completion_tokens: req.outputTokens,
      total_tokens: totalTokens,
    },
    choices: [
      {
        message: {
          role: "assistant",
          content: anthropicBody.completion?.trim(),
        },
        finish_reason: anthropicBody.stop_reason,
        index: 0,
      },
    ],
  };
}

export function transformAnthropicChatResponseToOpenAI(
  anthropicBody: Record<string, any>
): Record<string, any> {
  return {
    id: "ant-" + anthropicBody.id,
    object: "chat.completion",
    created: Date.now(),
    model: anthropicBody.model,
    usage: anthropicBody.usage,
    choices: [
      {
        message: {
          role: "assistant",
          content: flattenChatResponse(anthropicBody.content),
        },
        finish_reason: anthropicBody.stop_reason,
        index: 0,
      },
    ],
  };
}

/**
 * If a client using the OpenAI compatibility endpoint requests an actual OpenAI
 * model, reassigns it to Sonnet.
 */
function maybeReassignModel(req: Request) {
  const model = req.body.model;
  if (model.includes("claude")) return; // use whatever model the user requested
  req.body.model = "claude-3-5-sonnet-20240620";
}

/**
 * If client requests more than 4096 output tokens the request must have a
 * particular version header.
 * https://docs.anthropic.com/en/release-notes/api#july-15th-2024
 */
function setAnthropicBetaHeader(req: Request) {
  const { max_tokens_to_sample } = req.body;
  if (max_tokens_to_sample > 4096) {
    req.headers["anthropic-beta"] = "max-tokens-3-5-sonnet-2024-07-15";
  }
}

function selectUpstreamPath(manager: ProxyReqManager) {
  const req = manager.request;
  const pathname = req.url.split("?")[0];
  req.log.debug({ pathname }, "Anthropic path filter");

  const isChat = req.outboundApi === "anthropic-chat";
  if (isChat && pathname === "/v1/complete") {
    manager.setPath("/v1/messages");
  }

  if (isChat && pathname === "/v1/chat/completions") {
    manager.setPath("/v1/messages");
  }

  if (isChat && ["sonnet", "opus"].includes(req.params.type)) {
    manager.setPath("/v1/messages");
  }
}

const anthropicProxy = createQueuedProxyMiddleware({
  target: "https://api.anthropic.com",
  mutations: [selectUpstreamPath, addKey, finalizeBody],
  blockingResponseHandler: anthropicBlockingResponseHandler,
});

const nativeAnthropicChatPreprocessor = createPreprocessorMiddleware(
  { inboundApi: "anthropic-chat", outboundApi: "anthropic-chat", service: "anthropic" },
  { afterTransform: [setAnthropicBetaHeader] }
);

const oaiToChatPreprocessor = createPreprocessorMiddleware({
  inboundApi: "openai",
  outboundApi: "anthropic-chat",
  service: "anthropic",
});

/**
 * Routes an OpenAI prompt to either the legacy Claude text completion endpoint
 * or the new Claude chat completion endpoint, based on the requested model.
 */
const preprocessOpenAICompatRequest: RequestHandler = (req, res, next) => {
  maybeReassignModel(req);
  return oaiToChatPreprocessor(req, res, next);
};

const anthropicRouter = Router();
anthropicRouter.get("/v1/models", handleModelRequest);

// Native Anthropic chat completion endpoint.
anthropicRouter.post("/v1/messages", ipLimiter, nativeAnthropicChatPreprocessor, anthropicProxy);

// OpenAI-to-Anthropic compatibility endpoint. Accepts an OpenAI chat completion
// request and transforms/routes it to the appropriate Anthropic format and
// endpoint based on the requested model.
anthropicRouter.post(
  "/v1/chat/completions",
  ipLimiter,
  preprocessOpenAICompatRequest,
  anthropicProxy
);

export const anthropic = anthropicRouter;
