import type { ProxyReqMutator } from "../index";

/**
 * Removes origin and referer headers before sending the request to the API for
 * privacy reasons.
 */
export const stripHeaders: ProxyReqMutator = (manager) => {
  manager.removeHeader("host");
  manager.removeHeader("origin");
  manager.removeHeader("referer");

  // Some APIs refuse requests coming from browsers to discourage embedding
  // API keys in client-side code, so we must remove all CORS/fetch headers.
  Object.keys(manager.request.headers).forEach((key) => {
    switch (true) {
      case key.startsWith("sec-"):
      case key.startsWith("x-stainless-"):
      case key.startsWith("cf-"):
        manager.removeHeader(key);
        break;
    }
  });

  manager.removeHeader("tailscale-user-login");
  manager.removeHeader("tailscale-user-name");
  manager.removeHeader("tailscale-headers-info");
  manager.removeHeader("tailscale-user-profile-pic");
  manager.removeHeader("forwarded");
  manager.removeHeader("true-client-ip");
  manager.removeHeader("x-forwarded-for");
  manager.removeHeader("x-forwarded-host");
  manager.removeHeader("x-forwarded-proto");
  manager.removeHeader("x-real-ip");
  manager.removeHeader("user-agent");
};
