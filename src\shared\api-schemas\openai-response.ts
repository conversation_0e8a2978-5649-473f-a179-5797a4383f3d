import type { Request } from "express";
import { z } from "zod/v4";

import { config } from "@/config";
import { getModelFamilyForRequest, getModelMaxOutputLimit } from "../models";

const OpenAIInputContentSchema = z.discriminatedUnion("type", [
  z.object({ type: z.literal("input_text"), text: z.string() }),
  z.object({ type: z.literal("input_file"), filename: z.string(), file_url: z.string() }),
  z.object({
    type: z.literal("input_image"),
    detail: z.enum(["high", "low", "auto"]).default("auto"),
    image_url: z.string(),
  }),
]);

// https://platform.openai.com/docs/api-reference/Response/create
const OpenAIV1ResponseInputSchema = z.object({
  type: z.literal("message").optional(),
  role: z.enum(["user", "assistant", "system", "developer"]),
  content: z.union([z.string(), OpenAIInputContentSchema.array()]),
});

const OpenAIToolsSchema = z.discriminatedUnion("type", [
  z.object({ type: z.literal("local_shell") }),
  z.object({
    type: z.literal("function"),
    name: z.string(),
    parameters: z.record(z.string(), z.unknown()),
    strict: z.boolean().default(true),
    description: z.string().optional(),
  }),
  z.object({
    type: z.literal("mcp"),
    server_label: z.string(),
    server_url: z.string(),
    headers: z.record(z.string(), z.string()).nullish(),
    server_description: z.string().optional(),
    allowed_tools: z.union([
      z.string().array(),
      z.object({ tool_names: z.string().array() }).optional(),
    ]),
    require_approval: z
      .union([
        z.literal(["always", "never"]),
        z.object({
          always: z.object({ tool_names: z.string().array() }).optional(),
          never: z.object({ tool_names: z.string().array() }).optional(),
        }),
      ])
      .optional()
      .default("always"),
  }),
  z.object({
    type: z.literal(["web_search_preview", "web_search_preview_2025_03_11"]),
    search_context_size: z.enum(["low", "high", "medium"]).optional().default("medium"),
  }),
  z.object({
    type: z.literal("image_generation"),
    background: z.enum(["auto", "opaque", "transparent"]).optional().default("auto"),
    input_image_mask: z.object({ image_url: z.string() }).optional(),
    model: z.literal("gpt-image-1").optional(),
    output_compression: z.number().int().min(0).max(100).optional().default(100),
    output_format: z.enum(["png", "webp", "jpeg"]).optional().default("png"),
    partial_images: z.number().int().min(0).max(3).optional().default(0),
    quality: z.enum(["low", "high", "auto"]).optional().default("auto"),
    size: z.enum(["1024x1024", "1024x1536", "1536x1024", "auto"]).optional().default("auto"),
  }),
]);

const OpenAIToolChoiceSchema = z.union([
  z.enum(["none", "auto", "required"]),
  z.discriminatedUnion("type", [
    // Function tool
    z.object({ type: z.literal("function"), name: z.string() }),
    // MCP tool
    z.object({ type: z.literal("mcp"), server_label: z.string(), name: z.string().nullish() }),
    // Hosted tool
    z.object({ type: z.enum(["web_search_preview", "image_generation", "computer_use_preview"]) }),
  ]),
]);

export const OpenAIV1ResponseSchema = z.object({
  model: z.string(),
  instructions: z.string().nullish(),
  max_output_tokens: z.coerce.number().int().nullish(),

  input: z.union([z.string(), OpenAIV1ResponseInputSchema.array()]),

  max_tool_calls: z.coerce.number().int().nullish(),
  parallel_tool_calls: z.boolean().nullish().default(true),
  tool_choice: OpenAIToolChoiceSchema.optional(),
  tools: OpenAIToolsSchema.optional(),

  reasoning: z
    .object({
      effort: z.enum(["low", "medium", "high"]).nullish().default("medium"),
      summary: z.enum(["auto", "concise", "detailed"]).nullish(),
      generate_summary: z.enum(["auto", "concise", "detailed"]).nullish(),
    })
    .nullish(),

  service_tier: z.enum(["auto", "default", "priority", "flex"]).nullish().default("auto"),
  stream: z.boolean().nullish().default(false),
  store: z.boolean().nullish().default(false),

  temperature: z.coerce.number().int().nullish().default(1),
  top_p: z.coerce.number().min(0).max(1).nullish(),

  truncation: z.enum(["none", "disabled"]).nullish().default("disabled"),
  top_logprobs: z.coerce.number().int().min(0).max(20).nullish(),
});

export type OpenAIResponseInput = z.infer<typeof OpenAIV1ResponseSchema>["input"];

export function getOpenAIResponseSchema(req: Request) {
  return OpenAIV1ResponseSchema.transform((data) => {
    const modelFamily = getModelFamilyForRequest(req);

    const maxOutput =
      config.modelFamilySettings.get(modelFamily)?.maxOutput ?? config.defaultGlobalMaxOutput;
    const modelMaxOutput = getModelMaxOutputLimit(req);

    if (!config.allowToolUsage.includes(req.service!)) {
      delete data.tools;
      delete data.tool_choice;
    }

    if (
      !data.model.startsWith("o1") ||
      !data.model.startsWith("o3") ||
      !data.model.startsWith("o4")
    ) {
      delete data.reasoning;
    }

    const max_output_tokens = data.max_output_tokens ?? Number.MAX_SAFE_INTEGER;

    return {
      ...data,
      store: false,
      max_output_tokens: Math.min(max_output_tokens, modelMaxOutput, maxOutput),
    };
  });
}

export function containsImageContent(input: OpenAIResponseInput) {
  if (!Array.isArray(input)) return false;

  for (const { content } of input) {
    if (!Array.isArray(content)) continue;

    for (const contentItem of content) {
      if (contentItem.type === "input_image") return true;
    }
  }

  return false;
}
