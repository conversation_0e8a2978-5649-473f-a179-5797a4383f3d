import { RedisStore } from "connect-redis";
import cookieParser from "cookie-parser";
import expressSession from "express-session";
import MemoryS<PERSON> from "memorystore";
import { createClient } from "redis";

import { logger as baseLogger } from "@/logger";
import { config, SECRET_SIGNING_KEY } from "../config";

const logger = baseLogger.child({ module: "redis" });

export const redisClient = config.redisUrl
  ? await createClient({ url: config.redisUrl })
      .on("error", (err) => logger.error(err, "Redis error"))
      .connect()
  : undefined;

const store = redisClient
  ? new RedisStore({ client: redisClient, prefix: `${config.enviroment}:session:` })
  : new (MemoryStore(expressSession))({ checkPeriod: config.cookies.maxAge });

const cookieParserMiddleware = cookieParser(SECRET_SIGNING_KEY);
const sessionMiddleware = expressSession({
  secret: SECRET_SIGNING_KEY,
  resave: false,
  saveUninitialized: false,
  store,
  cookie: {
    signed: true,
    httpOnly: true,
    maxAge: config.cookies.maxAge,
    secure: config.cookies.secure,
    sameSite: config.cookies.sameSite,
  },
});

const withSession = [cookieParserMiddleware, sessionMiddleware];

export { withSession };
