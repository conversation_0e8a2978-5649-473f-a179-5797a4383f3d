PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_users-events` (
	`id` text PRIMARY KEY NOT NULL,
	`type` text NOT NULL,
	`payload` text NOT NULL,
	`userToken` text NOT NULL,
	`createdAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_users-events`("id", "type", "payload", "userToken", "createdAt") SELECT "id", "type", "payload", "userToken", "createdAt" FROM `users-events`;--> statement-breakpoint
DROP TABLE `users-events`;--> statement-breakpoint
ALTER TABLE `__new_users-events` RENAME TO `users-events`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE INDEX `idx_user-event` ON `users-events` (`userToken`,`type`);