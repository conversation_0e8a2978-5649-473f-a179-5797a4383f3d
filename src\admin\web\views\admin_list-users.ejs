<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "Users" }) %>
  </head>

  <body class="bg-background font-mono text-white">
    <div x-data="userList" class="mx-auto flex min-h-svh w-full flex-col gap-4 p-4">
      <h1 class="mt-2 mb-6 text-center text-3xl font-bold">User Management</h1>

      <div x-data="userList" class="space-y-4">
        <div>
          <!-- Loading state -->
          <div class="flex flex-col items-center justify-center gap-2 p-4" x-show="loading">
            <div
              class="border-accent h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"
            ></div>
            <p>Loading users...</p>
          </div>

          <!-- Error state -->
          <div
            class="border-border-error bg-background-error text-color-error my-4 rounded-lg border p-4"
            x-show="error"
            x-text="error"
          ></div>

          <!-- Content when data is loaded -->
          <div x-show="!loading && !error && users.length > 0">
            <div class="card mx-auto w-full max-w-7xl p-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="toggle-nicknames"
                    x-model="showNicknames"
                    class="text-accent focus:ring-accent h-4 w-4 cursor-pointer rounded border-gray-300 bg-gray-700"
                  />
                  <label for="toggle-nicknames" class="cursor-pointer text-base select-none"
                    >Show Nicknames</label
                  >
                </div>

                <button @click="fetchUsers()" class="secondary-button" title="Refresh data">
                  <span class="inline-block text-base">🔄</span> Refresh
                </button>
              </div>
            </div>
          </div>

          <!-- No users found message -->
          <div
            class="card bg-background-tertiary p-4 text-center text-lg"
            x-show="!loading && !error && users.length === 0"
          >
            No users found.
          </div>
        </div>

        <div x-show="!loading && !error && users.length > 0">
          <table class="card h-px w-full border-separate border-spacing-0 overflow-hidden p-0">
            <thead class="bg-background-secondary">
              <tr>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'token' }"
                >
                  <a href="#" @click.prevent="sortBy('token')">User</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'adminNote' }"
                >
                  <a href="#" @click.prevent="sortBy('adminNote')">Note</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'ipCount' }"
                >
                  <a href="#" @click.prevent="sortBy('ipCount')">IPs</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'promptsCount' }"
                >
                  <a href="#" @click.prevent="sortBy('promptsCount')">Prompts</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'inputTokens' }"
                >
                  <a href="#" @click.prevent="sortBy('inputTokens')">Input</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'outputTokens' }"
                >
                  <a href="#" @click.prevent="sortBy('outputTokens')">Output</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'type' }"
                >
                  <a href="#" @click.prevent="sortBy('type')">Type</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'createdAt' }"
                >
                  <a href="#" @click.prevent="sortBy('createdAt')">Created</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'lastUsedAt' }"
                >
                  <a href="#" @click.prevent="sortBy('lastUsedAt')">Last Used</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                  :class="{ 'text-accent-light': sortField === 'isDisabled' }"
                >
                  <a href="#" @click.prevent="sortBy('isDisabled')">Status</a>
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Actions
                </th>
              </tr>
            </thead>

            <tbody>
              <template x-for="(user, index) in paginatedUsers" :key="user.token">
                <tr
                  class="*:border-border transition-colors *:border-b hover:bg-[rgba(255,255,255,0.1)]"
                  :class="{ 'bg-background-tertiary': index % 2 === 1, '*:border-none': index === paginatedUsers.length - 1 }"
                >
                  <td class="p-2 px-3">
                    <a :href="`<%= proxyBasePath %>admin/manage/view-user/${user.token}`">
                      <code class="text-sm" x-show="!showNicknames" x-text="user.token"></code>
                      <template x-if="user.nickname">
                        <code class="text-sm" x-show="showNicknames" x-text="user.nickname"></code>
                      </template>

                      <template x-if="!user.nickname">
                        <code
                          class="text-sm"
                          x-show="showNicknames"
                          x-text="'...' + user.token.slice(-5)"
                        ></code>
                      </template>
                    </a>
                  </td>
                  <td class="p-2 px-3" x-text="user.adminNote ?? 'None'"></td>
                  <td class="p-2 px-3" x-text="user.ips"></td>
                  <td class="p-2 px-3" x-text="formatNumber(user.prompts)"></td>
                  <td class="p-2 px-3" x-text="formatNumber(user.inputTokens)"></td>
                  <td class="p-2 px-3" x-text="formatNumber(user.outputTokens)"></td>
                  <td class="p-2 px-3" x-text="user.type"></td>
                  <td class="p-2 px-3" x-text="formatDate(user.createdAt)"></td>
                  <td class="p-2 px-3" x-text="formatDate(user.lastUsedAt)"></td>
                  <td class="p-2 px-3">
                    <span
                      x-text="user.isDisabled ? 'Disabled' : 'Active'"
                      :class="user.isDisabled ? 'text-danger' : 'text-success'"
                      x-bind:title="user.disabledReason"
                    ></span>
                  </td>

                  <td class="h-full">
                    <div class="flex h-full items-center">
                      <template x-if="user.isDisabled || user.disabledAt">
                        <button
                          title="Reactivate User"
                          class="border-border hover:bg-success flex h-full flex-1 cursor-pointer items-center justify-center border-l p-0 transition-colors"
                          @click.prevent="unbanUser(user.token)"
                        >
                          <span class="inline-block text-base">✅</span>
                        </button>
                      </template>

                      <template x-if="!(user.isDisabled || user.disabledAt)">
                        <button
                          title="Disable User"
                          class="border-border hover:bg-danger flex h-full flex-1 cursor-pointer items-center justify-center border-l p-0 transition-colors"
                          @click.prevent="banUser(user.token)"
                        >
                          <span class="inline-block text-base">🚫</span>
                        </button>
                      </template>

                      <template x-if="user.isDisabled || user.disabledAt">
                        <button
                          title="Delete User"
                          class="border-border hover:bg-danger flex h-full flex-1 cursor-pointer items-center justify-center border-l p-0 transition-colors"
                          @click.prevent="deleteUser(user.token)"
                        >
                          <span class="inline-block text-base">🗑️</span>
                        </button>
                      </template>
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>

        <div
          id="pagination"
          class="mt-4 mb-2"
          x-show="!loading && !error && users.length > 0 && pageCount > 1"
        >
          <div class="flex flex-col items-center justify-center gap-4">
            <div class="flex items-center justify-center gap-2">
              <button
                class="primary-button"
                @click="changePage('prev')"
                :disabled="currentPage === 1"
                :class="{ 'cursor-not-allowed bg-[#25313c]': currentPage === 1 }"
              >
                &laquo; Prev
              </button>

              <template x-for="page in pageNumbers" :key="page">
                <button
                  class="primary-button h-10 w-10 p-0"
                  :class="{ 'bg-accent-hover': page === currentPage }"
                  @click="changePage(page)"
                  x-text="page"
                ></button>
              </template>

              <button
                class="primary-button"
                @click="changePage('next')"
                :disabled="currentPage === pageCount"
                :class="{ 'cursor-not-allowed bg-[#25313c]': currentPage === pageCount }"
              >
                Next &raquo;
              </button>
            </div>

            <p class="text-base" x-show="totalCount > 0">
              Showing <span x-text="(currentPage - 1) * pageSize + 1"></span> to
              <span x-text="Math.min(currentPage * pageSize, totalCount)"></span> of
              <span x-text="totalCount"></span> users
            </p>
          </div>
        </div>
      </div>

      <%- include("partials/admin-footer") %>
    </div>

    <script>
      document.addEventListener("alpine:init", () => {
        Alpine.data("userList", () => ({
          users: [],
          loading: true,
          error: null,

          // Pagination
          currentPage: 1,
          pageSize: 20,

          // Sorting
          sortField: null,
          sortDirection: null,

          dateFormatter: new Intl.DateTimeFormat("en-US", {
            dateStyle: "medium",
            timeStyle: "short",
          }),

          // Display options
          showNicknames: localStorage.getItem("showNicknames") === "true",

          // Get saved sort field from localStorage or use default
          getSavedSortField() {
            try {
              const options = JSON.parse(localStorage.getItem("admin:list-user:options")) || {};
              return options.sortField || "lastUsedAt";
            } catch (e) {
              return "lastUsedAt";
            }
          },

          // Get saved sort direction from localStorage or use default
          getSavedSortDirection() {
            try {
              const options = JSON.parse(localStorage.getItem("admin:list-user:options")) || {};
              return options.sortDirection || "desc";
            } catch (e) {
              return "desc";
            }
          },

          // Save sort options to localStorage
          saveSortOptions() {
            const options = {
              sortField: this.sortField,
              sortDirection: this.sortDirection,
            };
            localStorage.setItem("admin:list-user:options", JSON.stringify(options));
          },

          init() {
            // Load saved sort options
            this.sortField = this.getSavedSortField();
            this.sortDirection = this.getSavedSortDirection();

            this.fetchUsers();

            // Watch for changes in sort options and save them
            this.$watch("sortField", () => this.saveSortOptions());
            this.$watch("sortDirection", () => this.saveSortOptions());

            this.$watch("showNicknames", (value) => {
              localStorage.setItem("showNicknames", value);
            });
          },

          fetchUsers() {
            this.loading = true;
            this.error = null;

            fetch("/admin/api/users")
              .then((response) => {
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                return response.json();
              })
              .then((data) => {
                // Process the user data
                this.users = data.map((user) => ({
                  ...user,
                  createdAt: new Date(user.createdAt),
                  lastUsedAt: new Date(user.lastUsedAt),
                  isDisabled: user.isDisabled ?? false,
                  disabledReason: user.disabledReason ?? null,
                  disabledAt: user.disabledAt ? new Date(user.disabledAt) : null,
                }));
                this.loading = false;
              })
              .catch((error) => {
                this.error = `Failed to fetch users: ${error.message}`;
                this.loading = false;
                console.error("Error fetching users:", error);
              });
          },

          // Computed properties
          get sortedUsers() {
            return [...this.users].sort((a, b) => {
              let aValue = a[this.sortField];
              let bValue = b[this.sortField];

              // Handle null values
              if (aValue === null && bValue === null) return 0;
              if (aValue === null) return 1;
              if (bValue === null) return -1;

              // Compare based on type
              const result =
                typeof aValue === "string" ? aValue.localeCompare(bValue) : aValue - bValue;

              return this.sortDirection === "asc" ? result : -result;
            });
          },

          get totalCount() {
            return this.users.length;
          },

          get pageCount() {
            return Math.ceil(this.totalCount / this.pageSize);
          },

          get pageNumbers() {
            const pages = [];
            for (let i = 1; i <= this.pageCount; i++) {
              pages.push(i);
            }
            return pages;
          },

          get paginatedUsers() {
            const start = (this.currentPage - 1) * this.pageSize;
            const end = start + this.pageSize;
            return this.sortedUsers.slice(start, end);
          },

          // Methods
          sortBy(field) {
            if (this.sortField === field) {
              this.sortDirection = this.sortDirection === "asc" ? "desc" : "asc";
            } else {
              this.sortField = field;
              this.sortDirection = "desc";
            }

            // Save sort options immediately
            this.saveSortOptions();
          },

          changePage(direction) {
            if (direction === "prev") {
              this.currentPage = Math.max(1, this.currentPage - 1);
            } else if (direction === "next") {
              this.currentPage = Math.min(this.pageCount, this.currentPage + 1);
            } else if (typeof direction === "number") {
              this.currentPage = direction;
            }

            // Scroll to the pagination container after a short delay to allow DOM update
            this.$nextTick(() => {
              const paginationElement = document.getElementById("pagination");
              if (paginationElement) {
                // Scroll with smooth behavior
                paginationElement.scrollIntoView({ behavior: "smooth", block: "center" });
              }
            });
          },

          formatNumber(num) {
            return num.toLocaleString();
          },

          formatDate(timestamp) {
            if (!timestamp) return null;
            return this.dateFormatter.format(timestamp);
          },

          banUser(token) {
            if (confirm("Are you sure you want to disable this user?")) {
              const reason = prompt("Reason for disabling:");
              this.loading = true;

              fetch(`/admin/api/users/${token}/deactivate`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ disabledReason: reason }),
              })
                .then((response) => {
                  if (!response.ok) {
                    return response.json().then((data) => {
                      throw new Error(
                        data.error?.message || `HTTP error! Status: ${response.status}`
                      );
                    });
                  }
                  return response.json();
                })
                .then((data) => {
                  if (data.error) {
                    throw new Error(data.error.message || "Failed to disable user");
                  }
                  this.fetchUsers();
                })
                .catch((err) => {
                  this.error = "Failed to disable user: " + err.message;
                  this.loading = false;
                });
            }
          },

          unbanUser(token) {
            if (confirm("Are you sure you want to reactivate this user?")) {
              this.loading = true;

              fetch(`/admin/api/users/${token}/reactivate`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
              })
                .then((response) => {
                  if (!response.ok) {
                    return response.json().then((data) => {
                      throw new Error(
                        data.error?.message || `HTTP error! Status: ${response.status}`
                      );
                    });
                  }
                  return response.json();
                })
                .then((data) => {
                  if (data.error) {
                    throw new Error(data.error.message || "Failed to reactivate user");
                  }
                  this.fetchUsers();
                })
                .catch((err) => {
                  this.error = "Failed to reactivate user: " + err.message;
                  this.loading = false;
                });
            }
          },

          deleteUser(token) {
            if (
              confirm("Are you sure you want to DELETE this user? This action cannot be undone.")
            ) {
              this.loading = true;

              fetch(`/admin/api/users/${token}/delete`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({}),
              })
                .then((response) => {
                  if (!response.ok) {
                    return response.json().then((data) => {
                      throw new Error(
                        data.error?.message || `HTTP error! Status: ${response.status}`
                      );
                    });
                  }
                  return response.json();
                })
                .then((data) => {
                  if (data.error) {
                    throw new Error(data.error.message || "Failed to delete user");
                  }
                  this.fetchUsers();
                })
                .catch((err) => {
                  this.error = "Failed to delete user: " + err.message;
                  this.loading = false;
                });
            }
          },
        }));
      });
    </script>
  </body>
</html>
