import type { Request } from "express";
import { z } from "zod/v4";

import { config } from "@/config";

import { getModelFamilyForRequest, getModelMaxOutputLimit } from "../models";
import type { APIFormatTransformer } from "./index";
import { getOpenAISchema, type OpenAIChatMessage } from "./openai";

const AnthropicV1BaseSchema = z.object({
  model: z.string(),
  stop_sequences: z.array(z.string()).optional(),
  stream: z.boolean().optional().default(false),
  temperature: z.coerce.number().default(1).optional(),
  top_k: z.coerce.number().int().optional(),
  top_p: z.coerce.number().optional(),
  metadata: z.object({ user_id: z.string().optional() }).optional(),
});

const AnthropicV1MessageMultimodalContentSchema = z.array(
  z.discriminatedUnion("type", [
    z.object({ type: z.literal("text"), text: z.string() }),
    z.object({
      type: z.literal("image"),
      source: z.object({ type: z.literal("base64"), media_type: z.string(), data: z.string() }),
    }),
  ])
);

// https://docs.anthropic.com/claude/reference/messages_post
export const AnthropicV1MessagesSchema = AnthropicV1BaseSchema.extend({
  messages: z
    .object({
      role: z.enum(["user", "assistant"]),
      content: z.union([z.string(), AnthropicV1MessageMultimodalContentSchema]),
    })
    .array(),

  max_tokens: z.number().int().optional(),

  system: z
    .union([z.string(), z.array(z.object({ type: z.literal("text"), text: z.string() }))])
    .optional(),

  thinking: z
    .object({ budget_tokens: z.number().int().min(1024), type: z.literal("enabled") })
    .optional(),

  tools: z
    .object({
      name: z.string(),
      description: z.string().optional(),
      input_schema: z.record(z.string(), z.unknown()),
    })
    .optional(),

  tool_choice: z
    .discriminatedUnion("type", [
      z.object({ type: z.literal("none") }),
      z.object({ type: z.literal("any"), disable_parallel_tool_use: z.boolean().optional() }),
      z.object({ type: z.literal("auto"), disable_parallel_tool_use: z.boolean().optional() }),
      z.object({
        type: z.literal("tool"),
        disable_parallel_tool_use: z.boolean().optional(),
        name: z.string(),
      }),
    ])
    .optional(),
});

export function getAnthropicSchema(req: Request) {
  return AnthropicV1MessagesSchema.transform((data) => {
    const modelFamily = getModelFamilyForRequest(req);

    const maxOutput =
      config.modelFamilySettings.get(modelFamily)?.maxOutput ?? config.defaultGlobalMaxOutput;

    const modelMaxOutput = getModelMaxOutputLimit(req);

    if (!config.allowToolUsage.includes(req.service!)) {
      delete data.tools;
      delete data.tool_choice;
    }

    return {
      ...data,
      max_tokens: Math.min(data.max_tokens ?? Number.MAX_SAFE_INTEGER, modelMaxOutput, maxOutput),
    };
  });
}

export type AnthropicChatMessage = z.infer<typeof AnthropicV1MessagesSchema>["messages"][0];

export const transformOpenAIToAnthropicChat: APIFormatTransformer<
  typeof AnthropicV1MessagesSchema
> = async (req) => {
  const { body } = req;
  const result = getOpenAISchema(req).safeParse(body);

  if (!result.success) {
    req.log.warn({ issues: result.error.issues, body }, "Invalid OpenAI-to-Anthropic Chat request");
    throw result.error;
  }

  const { messages, ...rest } = result.data;
  const { messages: newMessages, system } = openAIMessagesToClaudeChatPrompt(messages);

  const metadata = rest.user ? { user_id: rest.user } : undefined;
  const stop_sequences = Array.from(
    new Set(rest.stop ? (Array.isArray(rest.stop) ? rest.stop : [rest.stop]) : [])
  );

  const max_tokens = Math.min(
    // Use max_completion_tokens/max_tokens from the body because the result is after transform
    // and it could be different from the original request.
    body.max_completion_tokens ?? body.max_tokens ?? Number.MAX_SAFE_INTEGER
  );

  const effortToBudget = { low: 1024, medium: 4096, high: 16384 };
  const thinkingBudget = body.reasoning_effort
    ? effortToBudget[body.reasoning_effort as keyof typeof effortToBudget]
    : undefined;

  const anthropicBody: z.infer<typeof AnthropicV1MessagesSchema> = {
    system,
    messages: newMessages,
    model: rest.model,
    max_tokens: max_tokens,
    stream: rest.stream,
    temperature: rest.temperature,
    top_p: rest.top_p,
    top_k: body.top_k,
    stop_sequences: stop_sequences.length > 0 ? stop_sequences : undefined,
    metadata,

    thinking: thinkingBudget
      ? { budget_tokens: Math.min(thinkingBudget, max_tokens), type: "enabled" }
      : undefined,
  };

  return getAnthropicSchema(req).parse(anthropicBody);
};

/**
 * Represents the union of all content types without the `string` shorthand
 * for `text` content.
 */
type AnthropicChatMessageContentWithoutString = Exclude<AnthropicChatMessage["content"], string>;
/** Represents a message with all shorthand `string` content expanded. */
type ConvertedAnthropicChatMessage = AnthropicChatMessage & {
  content: AnthropicChatMessageContentWithoutString;
};

function openAIMessagesToClaudeChatPrompt(messages: OpenAIChatMessage[]): {
  messages: AnthropicChatMessage[];
  system: string;
} {
  // Similar formats, but Claude doesn't use `name` property and doesn't have
  // a `system` role.  Also, Claude does not allow consecutive messages from
  // the same role, so we need to merge them.
  // 1. Collect all system messages up to the first non-system message and set
  // that as the `system` prompt.
  // 2. Iterate through messages and:
  //   - If the message is from system, reassign it to assistant with System:
  //     prefix.
  //   - If message is from same role as previous, append it to the previous
  //     message rather than creating a new one.
  //   - Otherwise, create a new message and prefix with `name` if present.

  // TODO: When a Claude message has multiple `text` contents, does the internal
  // message flattening insert newlines between them?  If not, we may need to
  // do that here...

  let firstNonSystem = -1;
  const result: { messages: ConvertedAnthropicChatMessage[]; system: string } = {
    messages: [],
    system: "",
  };
  for (let i = 0; i < messages.length; i++) {
    const msg = messages[i];
    const isSystem = isSystemOpenAIRole(msg.role);

    if (firstNonSystem === -1 && isSystem) {
      // Still merging initial system messages into the system prompt
      result.system += getFirstTextContent(msg.content) + "\n";
      continue;
    }

    if (firstNonSystem === -1 && !isSystem) {
      // Encountered the first non-system message
      firstNonSystem = i;

      if (msg.role === "assistant") {
        // There is an annoying rule that the first message must be from the user.
        // This is commonly not the case with roleplay prompts that start with a
        // block of system messages followed by an assistant message. We will try
        // to reconcile this by splicing the last line of the system prompt into
        // a beginning user message -- this is *commonly* ST's [Start a new chat]
        // nudge, which works okay as a user message.

        // Find the last non-empty line in the system prompt
        const execResult = /(?:[^\r\n]*\r?\n)*([^\r\n]+)(?:\r?\n)*/d.exec(result.system);

        let text = "";
        if (execResult) {
          text = execResult[1];
          // Remove last line from system so it doesn't get duplicated
          const [_, [lastLineStart]] = execResult.indices || [];
          result.system = result.system.slice(0, lastLineStart);
        } else {
          // This is a bad prompt; there's no system content to move to user and
          // it starts with assistant. We don't have any good options.
          text = "[ Joining chat... ]";
        }

        result.messages.push({
          role: "user",
          content: [{ type: "text", text }],
        });
      }
    }

    const last = result.messages[result.messages.length - 1];
    // I have to handle tools as system messages to be exhaustive here but the
    // experience will be bad.
    const role = isSystemOpenAIRole(msg.role) ? "assistant" : msg.role;

    // Here we will lose the original name if it was a system message, but that
    // is generally okay because the system message is usually a prompt and not
    // a character in the chat.
    const name = msg.role === "system" || msg.role === "developer" ? "System" : msg.name?.trim();

    const content = convertOpenAIContent(msg.content);

    // Prepend the display name to the first text content in the current message
    // if it exists. We don't need to add the name to every content block.
    if (name?.length) {
      const firstTextContent = content.find((c) => c.type === "text");
      if (firstTextContent && "text" in firstTextContent) {
        // This mutates the element in `content`.
        firstTextContent.text = `${name}: ${firstTextContent.text}`;
      }
    }

    // Merge messages if necessary. If two assistant roles are consecutive but
    // had different names, the final converted assistant message will have
    // multiple characters in it, but the name prefixes should assist the model
    // in differentiating between speakers.
    if (last && last.role === role) {
      last.content.push(...content);
    } else {
      result.messages.push({ role, content });
    }
  }

  result.system = result.system.trimEnd();
  return result;
}

function isSystemOpenAIRole(
  role: OpenAIChatMessage["role"]
): role is "developer" | "system" | "function" | "tool" {
  return ["developer", "system", "function", "tool"].includes(role);
}

function getFirstTextContent(content: OpenAIChatMessage["content"]) {
  if (typeof content === "string") return content;
  for (const c of content) {
    if ("text" in c) return c.text;
  }
  return "[ No text content in this message ]";
}

function convertOpenAIContent(
  content: OpenAIChatMessage["content"]
): AnthropicChatMessageContentWithoutString {
  if (typeof content === "string") {
    return [{ type: "text", text: content.trimEnd() }];
  }

  return content.map((c) => {
    if ("text" in c) {
      return { type: "text", text: c.text.trimEnd() };
    } else if ("image_url" in c) {
      const url = c.image_url.url;
      try {
        const mimeType = url.split(";")[0].split(":")[1];
        const data = url.split(",")[1];
        return {
          type: "image",
          source: { type: "base64", media_type: mimeType, data },
        };
      } catch (e) {
        return {
          type: "text",
          text: `[ Unsupported image URL: ${url.slice(0, 200)} ]`,
        };
      }
    } else {
      const type = String((c as any)?.type);
      return { type: "text", text: `[ Unsupported content type: ${type} ]` };
    }
  });
}

export function containsImageContent(messages: AnthropicChatMessage[]) {
  for (const message of messages) {
    if (typeof message.content === "string") continue;

    for (const content of message.content) {
      if (content.type === "image") return true;
    }
  }

  return false;
}
