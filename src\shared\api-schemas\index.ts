import type { Request } from "express";
import type { z } from "zod/v4";

import type { APIFormat } from "../key-management";

import { getAnthropicSchema, transformOpenAIToAnthropicChat } from "./anthropic";
import { getGoogleAISchema, transformOpenAIToGoogleAI } from "./google-ai";
import { getOpenAISchema } from "./openai";
import { getOpenAIResponseSchema } from "./openai-response";

export type { AnthropicChatMessage } from "./anthropic";
export type { GoogleAIChatMessage } from "./google-ai";
export type { OpenAIChatMessage } from "./openai";
export type { OpenAIResponseInput } from "./openai-response";

export type APIPair = `openai->${Exclude<APIFormat, "openai" | "openai-response">}`;
type TransformerMap = {
  [key in APIPair]?: APIFormatTransformer<any>;
};

export type APIFormatTransformer<Z extends z.ZodType<any, any>> = (
  req: Request
) => Promise<z.infer<Z>>;

export const API_REQUEST_TRANSFORMERS: TransformerMap = {
  "openai->google-ai": transformOpenAIToGoogleAI,
  "openai->anthropic-chat": transformOpenAIToAnthropicChat,
};

export function getAPIRequestSchema<T extends APIFormat>(req: Request, api: T) {
  const schema = {
    openai: getOpenAISchema,
    "openai-response": getOpenAIResponseSchema,

    "google-ai": getGoogleAISchema,
    "anthropic-chat": getAnthropicSchema,
  } as const;

  return schema[api](req);
}
