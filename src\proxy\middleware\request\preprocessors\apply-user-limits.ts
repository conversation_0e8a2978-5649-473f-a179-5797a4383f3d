import { isImageGenerationRequest, isTextGenerationRequest } from "@/proxy/middleware/common";
import { hasAvailableQuota, isUserHasAccess } from "@/shared/users/user-store";

import type { RequestPreprocessor } from "../index";

export class QuotaExceededError extends Error {
  public quotaInfo: { limit: number; used: number; requested?: number };
  constructor(message: string, quotaInfo: { limit: number; used: number; requested?: number }) {
    super(message);
    this.name = "QuotaExceededError";
    this.quotaInfo = quotaInfo;
  }
}

export class ModelNotAllowedError extends Error {
  public model: string;
  constructor(message: string, model: string) {
    super(message);
    this.name = "ModelNotAllowedError";
    this.model = model;
  }
}

export const applyUserLimits: RequestPreprocessor = async (req) => {
  const subjectToQuota = isTextGenerationRequest(req) || isImageGenerationRequest(req);
  if (!subjectToQuota || !req.user) return;

  const requestedTokens = (req.promptTokens ?? 0) + (req.outputTokens ?? 0);

  const isModelAllowed = await isUserHasAccess(req.user.token, req.body.model);
  if (!isModelAllowed) {
    throw new ModelNotAllowedError(
      `You do not have access to this model (${req.body.model}).`,
      req.body.model
    );
  }

  const quotaData = await hasAvailableQuota(req.user.token, {
    service: req.service,
    model: req.body.model,
    requested: requestedTokens,
  });

  if (quotaData.type !== "success") {
    throw new QuotaExceededError(
      `You have exceeded your ${quotaData.type} quota for this model type. (${req.modelFamily})`,
      {
        limit: quotaData.quota,
        used: quotaData.used,
        requested: quotaData.type === "tokens" ? requestedTokens : undefined,
      }
    );
  }
};
