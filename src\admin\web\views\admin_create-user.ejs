<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "Create User" }) %>
  </head>

  <body class="bg-background font-mono text-white">
    <div x-data="createUser" class="mx-auto flex w-full max-w-7xl flex-col gap-4 p-4">
      <h1 class="mt-2 mb-6 text-center text-3xl font-bold">Create User Token</h1>

      <div class="card mb-4 p-4">
        <h2 class="mb-4 text-2xl font-bold">User Token Types</h2>
        <ul class="list-disc space-y-2 pl-5">
          <li class="mb-2"><span class="font-bold">Normal</span> - Standard users.</li>
          <li class="mb-2">
            <span class="font-bold">Special</span> - Exempt from token quotas and
            <code>MAX_IPS_PER_USER</code> enforcement.
          </li>
          <li class="mb-2">
            <span class="font-bold">Temporary</span> - Disabled after run out of prompts.
          </li>
        </ul>
      </div>

      <div class="card mb-4 p-4">
        <h2 class="mb-4 text-2xl font-bold">Create New User</h2>

        <div class="mb-4">
          <div class="flex items-center gap-4">
            <label for="type" class="text-accent font-bold whitespace-nowrap">
              <span class="mr-2 inline-block w-max text-base">🔑</span> User Type:
            </label>

            <select
              id="type"
              x-model="userType"
              class="bg-background-tertiary border-border w-full rounded-lg border p-2 text-white"
            >
              <option value="normal">Normal</option>
              <option value="special">Special</option>
              <option value="temporary">Temporary</option>
            </select>
          </div>
        </div>

        <div class="mb-4">
          <div class="card bg-background-tertiary p-4" x-show="userType === 'normal'" x-transition>
            <p>
              <span class="font-bold">Normal User:</span> Standard user with default rate limits and
              quotas.
            </p>
          </div>
          <div class="card bg-background-tertiary p-4" x-show="userType === 'special'" x-transition>
            <p>
              <span class="font-bold">Special User:</span> Exempt from token quotas and IP
              restrictions.
            </p>
          </div>
          <div
            class="card bg-background-tertiary p-4"
            x-show="userType === 'temporary'"
            x-transition
          >
            <p>
              <span class="font-bold">Temporary User:</span> Limited-time access with prompt
              restrictions.
            </p>
          </div>
        </div>

        <div
          class="border-border mb-4 rounded-lg border p-4"
          id="temporaryUserOptions"
          x-show="userType === 'temporary'"
          x-transition
        >
          <h3 class="mb-2 text-xl font-bold">Temporary User Options</h3>
          <div class="space-y-4">
            <p>
              Temporary users will be disabled after the specified duration, and their records will
              be permanently deleted after some time. These options apply only to new temporary
              users; existing ones use whatever options were in effect when they were created.
            </p>
            <div>
              <label for="promptLimits" class="text-accent mb-2 block font-bold">
                <span class="mr-2 inline-block text-base">📈</span> Prompt Limits
              </label>
              <div class="flex items-center gap-4">
                <input
                  type="number"
                  id="promptLimits"
                  x-model.number="promptLimits"
                  min="1"
                  class="bg-background-tertiary border-border w-full rounded-lg border p-2 text-white"
                />
                <div
                  class="bg-background-tertiary border-border rounded-lg border px-3 py-1 text-sm whitespace-nowrap"
                  x-text="promptLimits + ' prompt' + (promptLimits > 1 ? 's' : '')"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div
          class="my-4 rounded-lg border border-[rgba(244,67,54,0.3)] bg-[rgba(244,67,54,0.1)] p-4 text-[#ff8a80]"
          x-show="error"
          x-text="error"
        ></div>

        <div class="mt-6">
          <button
            @click="createUser"
            class="primary-button w-full"
            :disabled="isSubmitting"
            :class="{ 'opacity-70 cursor-not-allowed': isSubmitting }"
          >
            <span class="mr-2 inline-block text-base">🔗</span>
            <span x-text="isSubmitting ? 'Creating...' : 'Create User'"></span>
          </button>
        </div>
      </div>

      <div
        class="card text-success mb-4 border border-[rgba(76,175,80,0.3)] bg-[rgba(76,175,80,0.1)] p-4"
        x-show="createdToken"
      >
        <div class="flex items-center gap-2">
          <span class="inline-block text-base">✅</span>
          <span>Just created: <code class="text-sm" x-text="createdToken"></code></span>
        </div>
      </div>

      <div class="card mb-4 p-4">
        <h2 class="mb-4 text-2xl font-bold">Recent Tokens</h2>
        <div class="space-y-2">
          <template x-if="recentTokens.length === 0">
            <p class="text-center text-gray-400">No recent tokens</p>
          </template>

          <ul class="space-y-2">
            <template x-for="token in recentTokens" :key="token">
              <li class="border-border border-b pb-2 last:border-0">
                <a
                  :href="'/admin/manage/view-user/' + token"
                  x-text="token"
                  class="text-accent hover:text-accent-light"
                  :class="{ 'font-bold': token === createdToken }"
                ></a>
              </li>
            </template>
          </ul>
        </div>
      </div>

      <!-- Floating Refresh Button -->
      <div
        class="secondary-button fixed right-4 bottom-4 flex size-13 items-center justify-center rounded-full text-lg"
        @click="createUser()"
        title="Create another user"
      >
        <span>➕</span>
      </div>
    </div>

    <script>
      document.addEventListener("alpine:init", () => {
        Alpine.data("createUser", () => ({
          userType: localStorage.getItem("admin__create-user__type") || "normal",
          promptLimits: 1,
          isSubmitting: false,
          error: null,
          createdToken: null,
          recentTokens: [],

          init() {
            // Watch for changes to userType and save to localStorage
            this.$watch("userType", (value) => {
              localStorage.setItem("admin__create-user__type", value);
            });

            // Load saved prompt limits if available
            const savedPromptLimits = localStorage.getItem("admin__create-user__promptLimits");
            if (savedPromptLimits) {
              this.promptLimits = parseInt(savedPromptLimits, 10) || 1;
            }

            // Watch for changes to promptLimits and save to localStorage
            this.$watch("promptLimits", (value) => {
              localStorage.setItem("admin__create-user__promptLimits", value);
            });

            // Load initial tokens from server-rendered data
            this.recentTokens = JSON.parse(
              `<%- JSON.stringify(recentTokens.map(user => user.token)) %>`
            );
          },

          createUser() {
            this.isSubmitting = true;
            this.error = null;

            // Prepare the request data
            const userData = {
              type: this.userType,
            };

            // Add prompt limits for temporary users
            if (this.userType === "temporary" && this.promptLimits > 0) {
              userData.promptLimits = this.promptLimits;
            }

            // Make the API request
            fetch("/admin/api/users/create", {
              method: "POST",
              headers: { "Content-Type": "application/json", "X-CSRF-Token": "<%= csrfToken %>" },
              body: JSON.stringify(userData),
            })
              .then((response) => {
                if (!response.ok) {
                  return response.json().then((data) => {
                    throw Error(data.error?.message || `HTTP error! Status: ${response.status}`);
                  });
                }
                return response.json();
              })
              .then((data) => {
                // Success! Store the created token
                this.createdToken = data.token;

                // Add to recent tokens list
                if (!this.recentTokens.includes(data.token)) {
                  this.recentTokens.unshift(data.token);
                  // Keep only the 5 most recent tokens
                  if (this.recentTokens.length > 5) {
                    this.recentTokens.pop();
                  }
                }

                this.isSubmitting = false;
              })
              .catch((err) => {
                this.error = `Failed to create user: ${err.message}`;
                this.isSubmitting = false;
              });
          },
        }));
      });
    </script>

    <!-- prettier-ignore -->
    <%- include("partials/admin-footer", { url: "/admin/manage/list-users", text: "Back to Users" }) %>
  </body>
</html>
