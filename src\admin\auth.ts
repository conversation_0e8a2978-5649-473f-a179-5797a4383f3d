import type { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { rateLimit } from "express-rate-limit";

import { config } from "../config";

export const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    const exemptIps = ["127.0.0.1", "::1"];
    return exemptIps.includes(req.ip) || !!req.session.adminUsername;
  },
  handler: (_req, res, _next, options) => {
    res.status(options.statusCode).json({
      error: {
        message: "Too many requests, please try again later.",
        status: options.statusCode,
      },
    });
  },
});

export const handleAPIAuthenticate: RequestHandler = (req, res, next) => {
  if (req.session && req.session.adminUsername) return next();

  const token = req.headers.authorization?.startsWith("Bearer ")
    ? req.headers.authorization.slice(7)
    : undefined;

  if (!token) {
    req.log.warn({ ip: req.ip }, "Blocked admin api request because no token was provided");

    return res.status(401).json({ error: { message: "Unauthorized", status: 401 } });
  }

  if (!config.adminKey) {
    req.log.warn({ ip: req.ip }, "Blocked admin api request because no admin key is configured");
    return res.status(401).json({ error: { message: "Unauthorized.", status: 401 } });
  }

  if (token !== config.adminKey) {
    req.log.warn(
      { ip: req.ip, invalidToken: token },
      "Attempted admin api request with invalid token"
    );

    return res.status(401).json({ error: { message: "Unauthorized.", status: 401 } });
  }

  return next();
};

export const handleWebAuthenticate: RequestHandler = (req, res, next) => {
  if (req.session && req.session.adminUsername) return next();
  return res.redirect("/admin/login?redirect=" + req.originalUrl);
};
