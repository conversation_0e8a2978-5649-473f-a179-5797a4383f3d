import { Router, type <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { cacheStore } from "@/shared/cache";
import { keyPool, type XA<PERSON><PERSON><PERSON> } from "@/shared/key-management";
import { isAllowedModel } from "@/shared/models";
import { createModelList } from "@/shared/utils";

import { addKey, createPreprocessorMiddleware, finalizeBody } from "../middleware/request";
import { createQueuedProxyMiddleware } from "../middleware/request/proxy-middleware-factory";
import type { ProxyResHandlerWithBody } from "../middleware/response";
import { ipLimiter } from "../rate-limit";

export function getModelsResponse() {
  const provider = keyPool.getKeyProvider("xai");
  if (provider.available() === 0) return [];

  const keys = provider.list() as XAIKey[];
  const modelIds = Array.from(new Set(keys.map((k) => k.modelIds).flat()));

  return createModelList(modelIds, "xai", (id) => isAllowedModel(id));
}

const handleModelRequest: RequestHandler = async (_req, res) => {
  const cache = await cacheStore.get<ReturnType<typeof getModelsResponse>>("xai");

  if (cache) {
    res.setHeader("Cache-State", "HIT");
    return res.status(200).json({ object: "list", data: cache });
  }

  const models = getModelsResponse();
  await cacheStore.set("xai", models);

  res.setHeader("Cache-State", "MISS");
  return res.status(200).json({ object: "list", data: models });
};

const xaiResponseHandler: ProxyResHandlerWithBody = async (_proxyRes, req, res, body) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  let newBody = body;
  res.status(200).json({ ...newBody, proxy: body.proxy });
};

const xaiProxy = createQueuedProxyMiddleware({
  mutations: [addKey, finalizeBody],
  target: "https://api.x.ai",
  blockingResponseHandler: xaiResponseHandler,
});

const xaiRouter = Router();
xaiRouter.get("/v1/models", handleModelRequest);

// General chat completion endpoint
xaiRouter.post(
  "/v1/chat/completions",
  ipLimiter,
  createPreprocessorMiddleware({ inboundApi: "openai", outboundApi: "openai", service: "xai" }),
  xaiProxy
);

// Redirect browser requests to the homepage.
xaiRouter.get("*", (req, res, next) => {
  const isBrowser = req.headers["user-agent"]?.includes("Mozilla");
  if (isBrowser) res.redirect("/");
  else next();
});

export const xai = xaiRouter;
