import type { ProxyR<PERSON>HandlerWithBody } from ".";
import { getModelFromBody, isTextGenerationRequest } from "../common";

import { config } from "@/config";

import { eventLogger } from "@/shared/logging";
import { hashIp } from "@/shared/users/user-store";
import { tryCatch } from "@/shared/utils";

/** If event logging is enabled, logs a chat completion event. */
export const logEvent: ProxyResHandlerWithBody = async (_proxyRes, req, _res, responseBody) => {
  if (!config.eventLogging) return;

  if (typeof responseBody !== "object") {
    throw new Error("Expected body to be an object");
  }

  const loggable = isTextGenerationRequest(req);
  if (!loggable) return;

  const model = getModelFromBody(req, responseBody);
  const userToken = req.user?.token;
  const family = req.modelFamily!;

  const promise = eventLogger.logEvent({
    userToken,
    type: "chat-completion",
    payload: {
      ip: hashIp(req.ip),
      model,
      family,
      inputTokens: req.promptTokens ?? 0,
      outputTokens: req.outputTokens ?? 0,
      keyHash: req.key!.hash,
    },
  });

  const [, err] = await tryCatch(() => promise);
  if (err) req.log.error(err, "Error logging event");
};
