import { doubleCsrf } from "csrf-csrf";
import express from "express";

import { config, SECRET_SIGNING_KEY } from "@/config";

const { generateCsrfToken, doubleCsrfProtection } = doubleCsrf({
  getSecret: () => SECRET_SIGNING_KEY,
  cookieName: "csrf",
  cookieOptions: {
    path: "/",
    httpOnly: true,
    secure: config.cookies.secure,
    maxAge: config.cookies.maxAge,
    sameSite: config.cookies.sameSite,
  },
  getSessionIdentifier: (req: express.Request) => req.session.id,
  getCsrfTokenFromRequest: (req: express.Request) => {
    const val: string =
      req.body["_csrf"] ||
      req.query["_csrf"] ||
      req.session.csrf ||
      req.cookies.csrf ||
      req.headers["x-csrf-token"];

    delete req.body["_csrf"];
    return val;
  },
});

const injectCsrfToken: express.RequestHandler = (req, res, next) => {
  const session = req.session;
  if (!session.csrf) session.csrf = generateCsrfToken(req, res, { validateOnReuse: true });

  res.locals.csrfToken = session.csrf;
  next();
};

export { injectCsrfToken, doubleCsrfProtection as checkCsrfToken };
