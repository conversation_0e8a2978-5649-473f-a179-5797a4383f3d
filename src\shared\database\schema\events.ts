import { ulid } from "ulid";

import { sql } from "drizzle-orm";
import { index, int, sqliteTable, text } from "drizzle-orm/sqlite-core";

export type EventLogPayloadData = {
  "chat-completion": {
    ip: string;
    model: string;
    family: string;
    inputTokens: number;
    outputTokens: number;
    keyHash: string;
  };
  "new-ip": {
    newIp: string;
  };
  "user-action": {
    action: string;
    payload?: Record<string, unknown>;
    ip: string;
  };
};

export type EventLogPayload<T extends keyof EventLogPayloadData> = EventLogPayloadData[T];

export const userEventType = ["new-ip", "user-action", "chat-completion"] as const;

export const users_events = sqliteTable(
  "users-events",
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => ulid()),

    type: text({ enum: userEventType }).notNull(),
    payload: text({ mode: "json" })
      .notNull()
      .$type<EventLogPayloadData[keyof EventLogPayloadData]>(),
    userToken: text().notNull(),

    createdAt: int({ mode: "timestamp" })
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
  },
  (table) => [index("idx_user-event").on(table.userToken, table.type)]
);
