import type { OpenAIChatCompletionStreamEvent } from "../index";

export type OpenAiResponseResponse = {
  id: string;
  object: "response";
  created_at: number;
  status: "completed";

  max_output_tokens: number;
  max_tool_calls: number;
  model: string;
  output: {
    id: string;
    type: "message";
    status: "completed";
    content: [{ type: "output_text"; text: string }];
    role: "assistant";
  }[];

  usage: {
    input_tokens: number;
    input_tokens_details: { cached_tokens: number };
    output_tokens: number;
    output_tokens_details: { reasoning_tokens: number };
    total_tokens: number;
  };
};

/**
 * Given a list of OpenAI chat completion events, compiles them into a single
 * finalized OpenAI chat completion response so that non-streaming middleware
 * can operate on it as if it were a blocking response.
 */
export function mergeEventsForOpenAIResponse(
  events: OpenAIChatCompletionStreamEvent[]
): OpenAiResponseResponse {
  let merged: OpenAiResponseResponse = {
    id: "",
    created_at: 0,
    max_output_tokens: 0,
    max_tool_calls: 0,
    model: "",
    output: [],
    usage: {
      input_tokens: 0,
      input_tokens_details: { cached_tokens: 0 },
      output_tokens: 0,
      output_tokens_details: { reasoning_tokens: 0 },
      total_tokens: 0,
    },
    object: "response",
    status: "completed",
  };

  merged = events.reduce((acc, event, i) => {
    // The first event will only contain role assignment and response metadata
    if (i === 0) {
      acc.id = event.id;
      acc.created_at = event.created;

      acc.model = event.model;
      acc.output = [
        {
          id: event.id,
          type: "message",
          status: "completed",
          content: [{ type: "output_text", text: "" }],
          role: "assistant",
        },
      ];
      return acc;
    }

    if (i === events.length - 1) {
      acc.usage = {
        input_tokens: event.usage?.prompt_tokens ?? 0,
        input_tokens_details: { cached_tokens: 0 },
        output_tokens: event.usage?.completion_tokens ?? 0,
        output_tokens_details: {
          reasoning_tokens: event.usage?.completion_tokens_details?.reasoning_tokens ?? 0,
        },
        total_tokens: event.usage?.total_tokens ?? 0,
      };
    }

    if (event.choices?.[0]?.delta?.content) {
      acc.output[0].content[0].text += event.choices[0].delta.content;
    }

    return acc;
  }, merged);
  return merged;
}
