#!/usr/bin/env bun

// Test script to verify the new logging configuration

import { config } from "../src/config";
import { logger } from "../src/logger";

console.log("Testing logging configuration...");
console.log("Current config:");
console.log("- Log Level:", config.logging.level);
console.log("- Log Output:", config.logging.output);
console.log("- Log Directory:", config.logging.directory);

// Test different log levels
logger.trace("This is a TRACE message");
logger.debug("This is a DEBUG message");
logger.info("This is an INFO message");
logger.warn("This is a WARN message");
logger.error("This is an ERROR message");

// Test with structured data
logger.info({ userId: "test123", action: "login" }, "User logged in");
logger.error({ error: "Connection failed", code: 500 }, "Database connection error");

console.log("Logging test completed. Check console and/or log files based on your configuration.");

if (config.logging.output === "file" || config.logging.output === "both") {
  console.log(`Log files should be created in: ${config.logging.directory}`);
  console.log("- default-YYYY-MM-DD.log (for trace, debug, info, warn)");
  console.log("- error-YYYY-MM-DD.log (for error messages)");
}
