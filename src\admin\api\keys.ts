import { sql } from "drizzle-orm";
import { Router } from "express";
import { z } from "zod/v4";

import { config } from "@/config";

import { getDatabase } from "@/shared/database";
import { schema } from "@/shared/database/database";
import { keyPool } from "@/shared/key-management";
import { LLM_SERVICES, type LLMService } from "@/shared/models";

const router = Router();

/**
 * Returns a list of all keys.
 *
 * GET /admin/api/keys
 */
router.get("/", (req, res) => {
  return res.json(keyPool.list());
});

/**
 * Retrieves the key provider for the specified service with pagination and sorting.
 *
 * GET /admin/api/keys/:service
 */
router.get("/:service", (req, res) => {
  const keyProvider = keyPool.getKeyProvider(req.params.service as LLMService);
  if (!keyProvider) {
    return res.status(404).json({ error: { message: "Service not found" } });
  }

  return res.json(keyProvider.list());
});

/**
 * Returns a specific key.
 *
 * GET /admin/api/keys/:service/:hash
 */
router.get("/:service/:hash", (req, res) => {
  const keyProvider = keyPool.getKeyProvider(req.params.service as LLMService);
  if (!keyProvider) {
    return res.status(404).json({ error: { message: "Service not found" } });
  }

  const key = keyProvider.list().find((key) => key.hash === req.params.hash);
  if (!key) {
    return res.status(404).json({ error: { message: "Key not found" } });
  }

  return res.json(key);
});

/**
 * Returns the logs for a specific key.
 *
 * GET /admin/api/keys/:service/:hash/logs
 */
router.get("/:service/:hash/logs", async (req, res) => {
  const keyProvider = keyPool.getKeyProvider(req.params.service as LLMService);
  if (!keyProvider) {
    return res.status(404).json({ error: { message: "Service not found" } });
  }

  const key = keyProvider.list().find((key) => key.hash === req.params.hash);
  if (!key) {
    return res.status(404).json({ error: { message: "Key not found" } });
  }

  const logs = await getDatabase()
    .select()
    .from(schema.users_events)
    .where(sql`json_extract(payload, '$.keyHash') = ${key.hash}`);

  return res.json(logs);
});

/**
 * Add a new key.
 *
 * POST /admin/api/keys/:service/add
 */
router.post("/:service/add", (req, res) => {
  const keyProvider = keyPool.getKeyProvider(req.params.service as LLMService);
  if (!keyProvider) {
    return res.status(404).json({ error: { message: "Service not found" } });
  }

  const inputSchema = z.object({ keys: z.array(z.string()).min(1, "Keys are required") });

  const result = inputSchema.safeParse(req.body);
  if (!result.success) {
    return res.status(400).json({ error: { message: z.prettifyError(result.error) } });
  }

  const addedKeys = keyPool.addKeys(keyProvider.service, result.data.keys);
  keyProvider.recheck();

  return res.json({ message: "Success", amount: addedKeys });
});

/**
 * Remove keys by hashes or type.
 *
 * POST /admin/api/keys/:service/delete
 */
router.post("/:service/delete", (req, res) => {
  const keyProvider = keyPool.getKeyProvider(req.params.service as LLMService);
  if (!keyProvider) {
    return res.status(404).json({ error: { message: "Service not found" } });
  }

  const inputSchema = z.discriminatedUnion("type", [
    z.object({ type: z.literal("overQuota") }),
    z.object({ type: z.literal("revoked") }),
    z.object({
      type: z.literal("hash"),
      hashes: z.array(z.string()).min(1, "Hashes are required"),
    }),
  ]);

  const result = inputSchema.safeParse(req.body);
  if (!result.success) {
    return res.status(400).json({ error: { message: z.prettifyError(result.error) } });
  }

  switch (result.data.type) {
    case "overQuota": {
      const removedKeys = keyPool.removeOverQuotaKeys(keyProvider.service);
      return res.json({ message: "Success", amount: removedKeys });
    }
    case "revoked": {
      const removedKeys = keyPool.removeRevokedKeys(keyProvider.service);
      return res.json({ message: "Success", amount: removedKeys });
    }

    case "hash": {
      const removedKeys = keyPool.removeKeys(keyProvider.service, result.data.hashes);
      return res.json({ message: "Success", amount: removedKeys });
    }
  }
});

/**
 * Enable a key by hash.
 *
 * POST /admin/api/keys/:service/:hash/enable
 */
router.post("/:service/:hash/enable", (req, res) => {
  const keyProvider = keyPool.getKeyProvider(req.params.service as LLMService);
  if (!keyProvider) {
    return res.status(404).json({ error: { message: "Service not found" } });
  }

  const key = keyProvider.list().find((k) => k.hash === req.params.hash);
  if (!key) {
    return res.status(404).json({ error: { message: "Key not found" } });
  }

  if (key.isRevoked) {
    return res.status(400).json({ error: { message: "Key is revoked and cannot be enabled." } });
  }

  keyProvider.update(key.hash, { isDisabled: false });
  return res.json({ message: "Success", key });
});

/**
 * Disable a key by hash.
 *
 * POST /admin/api/keys/:service/:hash/disable
 */
router.post("/:service/:hash/disable", (req, res) => {
  const keyProvider = keyPool.getKeyProvider(req.params.service as LLMService);
  if (!keyProvider) {
    return res.status(404).json({ error: { message: "Service not found" } });
  }

  const key = keyProvider.list().find((k) => k.hash === req.params.hash);
  if (!key) {
    return res.status(404).json({ error: { message: "Key not found" } });
  }

  keyProvider.update(key.hash, { isDisabled: true });
  return res.json({ message: "Success", key });
});

/**
 * Recheck a key service.
 *
 * POST /admin/api/keys/:service/recheck
 */
router.post("/:service/recheck", (req, res) => {
  if (!config.checkKeys) {
    return res.status(400).json({ error: { message: "Key checking is disabled" } });
  }

  const keyProvider = keyPool.getKeyProvider(req.params.service as LLMService);
  if (!keyProvider) {
    return res.status(404).json({ error: { message: "Service not found" } });
  }

  keyProvider.recheck();
  return res.json({ message: "Success", amount: keyProvider.list().length });
});

/**
 * Recheck all key services.
 *
 * POST /admin/api/keys/recheck
 */
router.post("/recheck", (_req, res) => {
  LLM_SERVICES.forEach((s) => keyPool.recheck(s));
  return res.json({ message: "Success", amount: keyPool.list().length });
});

export { router as keysApiRouter };
