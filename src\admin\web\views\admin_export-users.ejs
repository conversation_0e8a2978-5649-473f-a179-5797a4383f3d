<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "Export Users" }) %>
  </head>

  <body class="bg-background font-mono text-white">
    <div class="mx-auto flex min-h-svh w-full max-w-7xl flex-col gap-4 p-4">
      <h1 class="text-center text-3xl font-bold">Export Users</h1>

      <div class="flex flex-1 flex-col items-center justify-center gap-4">
        <div class="card w-full space-y-4 p-4">
          <h2 class="text-2xl font-bold">Export Format</h2>

          <p>
            Export users to JSON. The JSON will be an array of objects under the key
            <code>users</code>. You can use this JSON to import users later.
          </p>
        </div>

        <div class="card w-full space-y-4 p-4">
          <h2 class="text-2xl font-bold">Download JSON File</h2>
          <button class="primary-button w-full" onclick="exportUsers()">
            <span class="inline-block text-base">⬇️</span> Export Users
          </button>
        </div>
      </div>

      <!-- prettier-ignore -->
      <%- include("partials/admin-footer", { url: "/admin/manage/list-users", text: "Back to Users" }) %>

      <script>
        function exportUsers() {
          var xhr = new XMLHttpRequest();
          xhr.open("GET", "/admin/manage/export-users.json", true);
          xhr.responseType = "blob";
          xhr.onload = function () {
            if (this.status === 200) {
              var blob = new Blob([this.response], { type: "application/json" });
              var url = URL.createObjectURL(blob);
              var a = document.createElement("a");
              a.href = url;
              a.download = "users.json";
              document.body.appendChild(a);
              a.click();
              a.remove();
            }
          };
          xhr.send();
        }
      </script>
    </div>
  </body>
</html>
