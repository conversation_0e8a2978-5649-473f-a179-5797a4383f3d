<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "Download Stats" }) %>
  </head>

  <body class="bg-background font-mono text-white">
    <div class="mx-auto flex h-svh w-full max-w-7xl flex-col gap-4 p-4">
      <h1 class="mt-2 mb-6 text-center text-3xl font-bold">Download Stats</h1>

      <div class="card mb-4 p-4">
        <h2 class="mb-4 text-2xl font-bold">About Stats</h2>
        <p class="mb-4">
          Download usage statistics to a Markdown document. You can paste this into a service like
          Rentry.org to share it.
        </p>
      </div>

      <div class="card mb-4 p-4">
        <h2 class="mb-4 text-2xl font-bold">Options</h2>
        <form id="statsForm" action="/admin/manage/generate-stats" method="post" class="space-y-6">
          <input id="_csrf" type="hidden" name="_csrf" value="<%= csrfToken %>" />

          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label
                class="text-accent mb-2 flex cursor-pointer items-center gap-2 font-bold"
                for="anon"
              >
                <input
                  id="anon"
                  type="checkbox"
                  name="anon"
                  value="true"
                  class="text-accent focus:ring-accent h-4 w-4 cursor-pointer rounded border-gray-300 bg-gray-700"
                />
                <span>Anonymize User Data</span>
              </label>
            </div>

            <div>
              <label for="maxUsers" class="text-accent mb-2 block font-bold">Max Users</label>
              <input
                id="maxUsers"
                type="number"
                name="maxUsers"
                value="1000"
                class="bg-background-tertiary border-border w-full rounded-lg border p-2 text-white"
              />
            </div>
          </div>

          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label for="sort" class="text-accent mb-2 block font-bold">Sort By</label>
              <select
                id="sort"
                name="sort"
                class="bg-background-tertiary border-border w-full rounded-lg border p-2 text-white"
              >
                <option value="tokens" selected>Token Count</option>
                <option value="prompts">Prompt Count</option>
              </select>
            </div>

            <div>
              <label for="tableType" class="text-accent mb-2 block font-bold">Table Format</label>
              <select
                id="tableType"
                name="tableType"
                class="bg-background-tertiary border-border w-full rounded-lg border p-2 text-white"
              >
                <option value="markdown" selected>Markdown Table</option>
                <option value="code">Code Block</option>
              </select>
            </div>
          </div>

          <div>
            <label for="format" class="text-accent mb-2 block font-bold">Custom Format</label>
            <ul class="mb-2 space-y-1 pl-4">
              <li><code>{{header}}</code> - Summary of stats</li>
              <li><code>{{stats}}</code> - Table of user data</li>
              <li><code>{{time}}</code> - Current timestamp</li>
            </ul>
            <textarea
              id="format"
              name="format"
              rows="6"
              placeholder="{{stats}}"
              class="bg-background-tertiary border-border min-h-[150px] w-full resize-y rounded-lg border p-2 text-white"
            >
# Stats
{{header}}
{{stats}}
{{time}}</textarea
            >
          </div>

          <div class="flex flex-col gap-4 sm:flex-row">
            <button type="submit" class="primary-button flex-1 py-3">
              <span class="mr-2 inline-block text-base">⬇️</span> Download Stats
            </button>
            <button id="copyButton" type="button" class="secondary-button flex-1 py-3">
              <span class="mr-2 inline-block text-base">📋</span> Copy to Clipboard
            </button>
          </div>
        </form>
      </div>

      <script>
        function loadDefaults() {
          const getState = (key) => localStorage.getItem("admin__download-stats__" + key);
          const setState = (key, value) =>
            localStorage.setItem("admin__download-stats__" + key, value);

          const checkboxes = ["anon"];
          const values = ["sort", "format", "tableType", "maxUsers"];

          checkboxes.forEach((key) => {
            const value = getState(key);
            if (value) {
              document.getElementById(key).checked = value == "true";
            }
            document.getElementById(key).addEventListener("change", (e) => {
              setState(key, e.target.checked);
            });
          });

          values.forEach((key) => {
            const value = getState(key);
            if (value) {
              document.getElementById(key).value = value;
            }
            document.getElementById(key).addEventListener("change", (e) => {
              setState(key, e.target.value?.trim());
            });
          });
        }

        loadDefaults();

        async function fetchAndCopy() {
          const form = document.getElementById("statsForm");
          const formData = new FormData(form);

          try {
            const response = await fetch(form.action, {
              method: "POST",
              headers: { "Content-Type": "application/x-www-form-urlencoded" },
              credentials: "same-origin",
              body: new URLSearchParams(formData),
            });

            if (response.ok) {
              const content = await response.text();
              copyToClipboard(content);
            } else {
              throw new Error("Failed to fetch generated stats. Try reloading the page.");
            }
          } catch (error) {
            alert("Error: " + error.message);
          }
        }

        function copyToClipboard(text) {
          navigator.clipboard
            .writeText(text)
            .then(() => {
              alert("Copied to clipboard");
            })
            .catch((err) => {
              alert("Failed to copy to clipboard. Try downloading the file instead.");
            });
        }

        document.getElementById("copyButton").addEventListener("click", fetchAndCopy);
      </script>

      <%- include("partials/admin-footer") %>
    </div>
  </body>
</html>
