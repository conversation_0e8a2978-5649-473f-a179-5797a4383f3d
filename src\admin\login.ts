import { Router } from "express";
import { rateLimit } from "express-rate-limit";

import { config } from "../config";

const loginLimiter = rateLimit({
  max: 10,
  windowMs: 15 * 60 * 1000,
  handler: (_req, res, _next, options) => {
    return res.status(options.statusCode).json({
      error: {
        message: "Too many requests for this IP, please try again later.",
        status: options.statusCode,
      },
    });
  },
});

const loginRouter = Router();

loginRouter.get("/", (req, res) => {
  if (req.session.adminUsername) return res.redirect("/admin/manage");
  res.redirect("/admin/login");
});

loginRouter.get("/login", (req, res) => {
  if (req.session.adminUsername) res.redirect("/admin/manage");
  res.render("admin_login", { redirect: req.query.redirect });
});

loginRouter.post("/login", loginLimiter, (req, res, next) => {
  const { password, username, redirect } = req.body;
  if (!password || !username) {
    return res.status(400).json({
      error: {
        message: "Missing username or password!",
        status: 400,
      },
    });
  }

  if (!config.adminKey) {
    req.log.warn({ ip: req.ip }, "Blocked admin request because no admin key is configured");
    return res.status(401).json({ error: { message: "Unauthorized.", status: 401 } });
  }

  if (password !== config.adminKey) {
    req.log.warn(
      { ip: req.ip, invalidToken: password },
      "Attempted admin request with invalid token"
    );

    res.clearCookie("adminToken");
    delete req.session.adminUsername;
    req.session.save();

    return res.status(401).json({ error: { message: "Your password is incorrect.", status: 401 } });
  }

  const csrf = req.session.csrf;

  req.session.regenerate((err) => {
    if (err) return next(err);

    req.session.csrf = csrf;
    req.session.adminUsername = username;

    req.session.save(function (err) {
      if (err) return next(err);
      return res.status(200).json({
        success: true,
        redirectUrl: redirect || "/admin/manage",
      });
    });
  });
});

loginRouter.get("/logout", (req, res, next) => {
  delete req.session.adminUsername;
  res.clearCookie("adminToken");

  req.session.save(function (err) {
    if (err) return next(err);

    req.session.regenerate(function (err) {
      if (err) return next(err);
      res.redirect("/admin/login");
    });
  });
});

export { loginRouter };
