CREATE TABLE `users-limits` (
	`userToken` text NOT NULL,
	`limitNextReset` integer,
	`limitReset` text DEFAULT '[]' NOT NULL,
	`modelFamily` text NOT NULL,
	`promptLimits` integer DEFAULT 0 NOT NULL,
	`promptsUsed` integer DEFAULT 0 NOT NULL,
	`tokenLimits` integer DEFAULT 0 NOT NULL,
	`tokensUsed` integer DEFAULT 0 NOT NULL,
	`createdAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	`updatedAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	FOREIGN KEY (`userToken`) REFERENCES `users`(`token`) ON UPDATE cascade ON DELETE cascade
);
--> statement-breakpoint
ALTER TABLE `users` ADD `disAllowedModels` text DEFAULT '[]' NOT NULL;