import type { NextFunction, Request, Response } from "express";

import { config } from "@/config";
import { logger as baseLogger } from "@/logger";

import { getModelFamilyForModel } from "@/shared/models";
import { tryCatchSync } from "@/shared/utils";

const logger = baseLogger.child({ module: "rate-limit" });

class RateLimitError extends Error {
  consumedPoints: number;
  remainingPoints: number;
  msBeforeNext: number;

  constructor(
    message: string,
    {
      consumedPoints,
      remainingPoints,
      msBeforeNext,
    }: { consumedPoints: number; remainingPoints: number; msBeforeNext: number }
  ) {
    super(message);
    this.name = "RateLimitError";
    this.consumedPoints = consumedPoints;
    this.remainingPoints = remainingPoints;
    this.msBeforeNext = msBeforeNext;
  }
}

class TokenBucket {
  private capacity: number;
  private refillRate: number;
  private refillInterval: number;
  points: number;
  private lastRefill: number;

  constructor(options: { capacity: number; refillRate: number; refillInterval: number }) {
    this.capacity = options.capacity;
    this.refillRate = options.refillRate;
    this.refillInterval = options.refillInterval;
    this.points = this.capacity;
    this.lastRefill = Date.now();
  }

  private refill(): void {
    const now = Date.now();
    const timeSinceLastRefill = now - this.lastRefill;
    const refills = Math.floor(timeSinceLastRefill / this.refillInterval);

    if (refills > 0) {
      this.points = Math.min(this.capacity, this.points + refills * this.refillRate);
      this.lastRefill = now;
    }
  }

  consume(points: number): boolean {
    this.refill();
    if (this.points >= points) {
      this.points -= points;
      return true;
    }
    return false;
  }

  refund(points: number): void {
    this.points = Math.min(this.capacity, this.points + points);
  }

  isFull(): boolean {
    this.refill();
    return this.points === this.capacity;
  }
}

class RateLimiter {
  private buckets: Map<string, TokenBucket> = new Map();
  private activeRequests: Set<string> = new Set();
  private options: {
    capacity: number;
    refillRate: number;
    refillInterval: number;
  };

  constructor(options: { capacity: number; refillRate: number; refillInterval: number }) {
    this.options = options;
    setInterval(() => this.cleaner(), 60 * 1000);
  }

  consume(key: string, points: number) {
    let bucket = this.buckets.get(key);
    if (!bucket) {
      bucket = new TokenBucket(this.options);
      this.buckets.set(key, bucket);
    }

    if (bucket.consume(points)) {
      return { remainingPoints: bucket.points, msBeforeNext: 0 };
    }

    const now = Date.now();
    const timeSinceLastRefill = now - bucket["lastRefill"];
    const nextRefillTime =
      bucket["refillInterval"] - (timeSinceLastRefill % bucket["refillInterval"]);

    throw new RateLimitError("Rate limit exceeded", {
      consumedPoints: points,
      remainingPoints: bucket.points,
      msBeforeNext: nextRefillTime,
    });
  }

  refund(key: string, points: number): void {
    const bucket = this.buckets.get(key);
    if (bucket) {
      bucket.refund(points);
    }
  }

  getActiveRequests(): string[] {
    return Array.from(this.activeRequests.keys());
  }

  start(key: string): void {
    this.activeRequests.add(key);
  }

  finish(key: string): void {
    this.activeRequests.delete(key);
  }

  private cleaner(): void {
    logger.debug("Running rate limiter cleaner...");
    let cleaned = 0;
    for (const [key, bucket] of this.buckets.entries()) {
      if (bucket.isFull()) {
        this.buckets.delete(key);
        cleaned++;
      }
    }
    logger.debug(`Cleaned ${cleaned} buckets.`);
  }
}

const ratelimiter = new RateLimiter(config.ratelimitConfig);

export async function refundPoints(req: Request) {
  const ratelimitKey = req.user?.token || req.risuToken || req.ip;

  const modelFamily = getModelFamilyForModel(req);
  const pointsToRefund = config.modelFamilySettings.get(modelFamily)?.weight ?? 1;

  ratelimiter.refund(ratelimitKey, pointsToRefund);
  logger.debug(`[REFUND] Successfully refunded ${pointsToRefund} points to key ${ratelimitKey}.`);
}

export function getActiveRequests(): number {
  return ratelimiter.getActiveRequests().length;
}

export async function ipLimiter(req: Request, res: Response, next: NextFunction) {
  const modelFamily = getModelFamilyForModel(req);

  // If user is authenticated, key rate limiting by their token. Otherwise, key
  // rate limiting by their IP address. Mitigates key sharing.
  const rateLimitKey = req.user?.token || req.risuToken || req.ip;
  const requestWeight = config.modelFamilySettings.get(modelFamily)?.weight ?? 1;

  const [result, error] = tryCatchSync(
    () => ratelimiter.consume(rateLimitKey, requestWeight),
    RateLimitError
  );

  if (error) {
    if (error instanceof RateLimitError) {
      const secondsToWait = Math.ceil(error.msBeforeNext / 1000);

      res.setHeader("Retry-After", secondsToWait);
      res.setHeader("X-RateLimit-Remaining", error.remainingPoints);
      res.setHeader("X-RateLimit-Limit", config.ratelimitConfig.capacity);
      res.setHeader("X-RateLimit-Reset", Math.ceil((Date.now() + error.msBeforeNext) / 1000));

      return res.status(429).json({
        error: {
          type: "rate_limit_error",
          message: `You have exceeded your request limit. Please try again in ${secondsToWait} second(s).`,
          tokensNeeded: error.consumedPoints,
          tokensRemaining: error.remainingPoints,
        },
      });
    }

    return res.status(500).json({ error: { type: "internal_error", message: error.message } });
  }

  res.setHeader("X-RateLimit-Remaining", result.remainingPoints);
  res.setHeader("X-RateLimit-Limit", config.ratelimitConfig.capacity);
  res.setHeader("X-RateLimit-Reset", Math.ceil((Date.now() + result.msBeforeNext) / 1000));

  ratelimiter.start(rateLimitKey);
  res.on("finish", () => {
    ratelimiter.finish(rateLimitKey);
    logger.debug(`[FINISH] Removed request from active list for key ${rateLimitKey}.`);
  });

  return next();
}
