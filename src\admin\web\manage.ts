import { Router } from "express";

import { getDatabase } from "@/shared/database";
import { LLM_SERVICES } from "@/shared/models";

const router = Router();

router.get("/", (_req, res) => {
  return res.render("admin_index");
});

router.get("/create-user", async (_req, res) => {
  const recentTokens = await getDatabase().query.users.findMany({
    orderBy: (fields, { desc }) => desc(fields.createdAt),
    columns: { token: true },
    limit: 5,
  });

  return res.render("admin_create-user", { recentTokens });
});

router.get("/view-user/:token", (req, res) => {
  return res.render("admin_view-user", { params: req.params });
});

router.get("/list-users", (_req, res) => {
  return res.render("admin_list-users");
});

router.get("/import-users", (_req, res) => {
  return res.render("admin_import-users");
});

router.get("/export-users", (_req, res) => {
  return res.render("admin_export-users");
});

router.get("/export-users.json", async (_req, res) => {
  const users = await getDatabase().query.users.findMany({
    with: { ips: { with: { usages: true } }, usages: true },
  });

  res.setHeader("Content-Disposition", "attachment; filename=users.json");
  res.setHeader("Content-Type", "application/json");

  return res.send(JSON.stringify({ users }, null, 2));
});

router.get("/list-keys", (req, res) => {
  return res.render("admin_list-keys", {
    service: req.query.service ?? "openai",
    services: LLM_SERVICES,
  });
});

router.get("/view-key/:service/:hash", (req, res) => {
  return res.render("admin_view-key", { params: req.params });
});

export { router as usersWebRouter };
