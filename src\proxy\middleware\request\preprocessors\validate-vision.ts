import { config } from "@/config";

import { ForbiddenError } from "@/shared/errors";
import { assertNever } from "@/shared/utils";

import { containsImageContent as containsImageContentAnthropic } from "@/shared/api-schemas/anthropic";
import { containsImageContent as containsImageContentGoogle } from "@/shared/api-schemas/google-ai";
import { containsImageContent as containsImageContentOpenAI } from "@/shared/api-schemas/openai";
import { containsImageContent as containsImageContentOpenAIResponse } from "@/shared/api-schemas/openai-response";

import type { RequestPreprocessor } from "../index";

/**
 * Rejects prompts containing images if multimodal prompts are disabled.
 */
export const validateVision: RequestPreprocessor = async (req) => {
  if (req.service === undefined) {
    throw new Error("Request service must be set before validateVision");
  }

  if (req.user?.type === "special") return;
  if (config.allowedVisionServices.includes(req.service)) return;

  // vision not allowed for req's service, block prompts with images
  let hasImage = false;
  switch (req.outboundApi) {
    case "openai":
      hasImage = containsImageContentOpenAI(req.body.messages);
      break;

    case "openai-response":
      hasImage = containsImageContentOpenAIResponse(req.body.input);
      break;

    case "anthropic-chat":
      hasImage = containsImageContentAnthropic(req.body.messages);
      break;

    case "google-ai":
      hasImage = containsImageContentGoogle(req.body.contents);
      break;

    default:
      assertNever(req.outboundApi);
  }

  if (hasImage) {
    throw new ForbiddenError(
      "Prompts containing images are not permitted. Disable 'Send Inline Images' in your client and try again."
    );
  }
};
