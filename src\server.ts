import cors from "cors";
import express from "express";
import path from "path";
import pinoHttp from "pino-http";
import chalk from "chalk";

import { adminRouter } from "./admin/routes";
import { assertConfigIsValid, config, USER_ASSETS_DIR } from "./config";
import { infoPageRouter } from "./info-page";
import { logger } from "./logger";
import { checkOrigin } from "./proxy/check-origin";
import { sendErrorToClient } from "./proxy/middleware/response/error-generator";
import { start as startRequestQueue } from "./proxy/queue";
import { proxyRouter } from "./proxy/routes";

import { createBlacklistMiddleware } from "./shared/cidr";
import { getDatabase, initializeDatabase } from "./shared/database";
import { keyPool } from "./shared/key-management";
import { init as initTokenizers } from "./shared/tokenization";
import { init as initUserStore } from "./shared/users/user-store";

import { userRouter } from "./user/routes";

const PORT = config.port;
const BIND_ADDRESS = config.bindAddress;

const app = express();
app.disable("x-powered-by");
const mainRouter = express.Router();

// middleware
app.use(
  pinoHttp({
    quietReqLogger: true,
    logger,
    customSuccessMessage: function (req, res, responseTime) {
      const success = res.statusCode < 400;
      const message = success ? "Request complete" : "Request failed";

      const colors = success ? chalk.green : chalk.red;

      return `${message} - ${req.method} ${colors(res.statusCode)} ${req.url} - ${responseTime}ms`;
    },
    customErrorMessage: function (req, res, err) {
      return `Request failed - ${req.method} ${chalk.red(res.statusCode)} ${req.url} - ${err.message}`;
    },
    autoLogging: {
      ignore: ({ url }) => {
        const ignoreList = [
          "/health",
          path.join(config.proxyBasePath, "res"),
          path.join(config.proxyBasePath, "user_content"),
          path.join(config.proxyBasePath, "public"),
        ];
        return ignoreList.some((path) => (url as string).startsWith(path));
      },
    },
    redact: {
      paths: [
        "req.headers.host",
        "req.headers.cookie",
        "req.headers.authorization",
        'req.headers["api-key"]',
        'req.headers["x-api-key"]',
        'req.headers["x-real-ip"]',
        'res.headers["set-cookie"]',
        'req.headers["x-goog-api-key"]',
        'req.headers["x-forwarded-for"]',
        'req.headers["cf-connecting-ip"]',
        'req.headers["cf-ipcountry"]',

        // Don't log the prompt text on transform errors
        "body.messages",
        "body.prompt",
        "body.contents",
      ],
      censor: "********",
    },
  })
);

app.set("trust proxy", Number(config.trustedProxies));

app.set("view engine", "ejs");
app.set("views", [
  "./public/server/admin/web/views",
  "./public/server/user/web/views",
  "./public/server/shared/views",

  path.join(__dirname, "admin/web/views"),
  path.join(__dirname, "user/web/views"),
  path.join(__dirname, "shared/views"),
]);

mainRouter.use("/user_content", express.static(USER_ASSETS_DIR, { maxAge: "2h" }));
mainRouter.use(
  "/res",
  express.static(path.join(__dirname, "..", "public"), {
    maxAge: "2h",
    etag: false,
    index: false,
    setHeaders: (res, path) => {
      if (path.endsWith(".html")) {
        res.setHeader("Content-Type", "text/plain; charset=utf-8");
      }
    },
  })
);

mainRouter.get("/health", (_req, res) => res.sendStatus(200));
app.use(cors());

const blacklist = createBlacklistMiddleware("IP_BLACKLIST", config.ipBlacklist);
app.use(blacklist);
app.use(checkOrigin);

mainRouter.use("/admin", adminRouter);
mainRouter.use((req, _, next) => {
  // For whatever reason SillyTavern just ignores the path a user provides
  // when using Google AI with reverse proxy. We'll fix it here.
  if (req.path.match(/^\/v1(alpha|beta)\/models\//)) {
    req.url = `${config.proxyEndpointRoute}/google-ai${req.url}`;
    return next();
  }
  next();
});
mainRouter.use(config.proxyEndpointRoute, proxyRouter);
mainRouter.use("/user", userRouter);

if (config.staticServiceInfo) {
  mainRouter.get("/", (_req, res) => res.sendStatus(200));
} else {
  mainRouter.use("/", infoPageRouter);
}
app.use(config.proxyBasePath, mainRouter);

// 500 and 404
app.use((err: any, req: express.Request, res: express.Response, _next: unknown) => {
  if (!err.status) {
    logger.error(err, "Unhandled error in request");
  }

  sendErrorToClient({
    req,
    res,
    options: {
      title: `Proxy error (HTTP ${err.status})`,
      message: "Reverse proxy encountered an unexpected error while processing your request.",
      reqId: req.id,
      statusCode: err.status,
      obj: { error: err.message, stack: err.stack },
      format: "unknown",
    },
  });
});

app.use((_req: unknown, res: express.Response) => {
  res.status(404).json({ error: { message: "Not found" } });
});

async function start() {
  logger.info("Server starting up...");
  await setBuildInfo();

  logger.info("Checking configs and external dependencies...");
  await assertConfigIsValid();

  keyPool.init();

  await initTokenizers();

  if (
    config.eventLogging ||
    (config.gatekeeperStore !== "memory" && config.gatekeeper === "user_token")
  ) {
    await initializeDatabase();
  }

  if (config.gatekeeper === "user_token") {
    await initUserStore();
  }

  if (process.env.NODE_ENV === "production") {
    await Bun.$`bun build:css:prod`.nothrow().quiet();
  }

  logger.info("Starting request queue...");
  startRequestQueue();

  app.listen(PORT, BIND_ADDRESS, () => {
    logger.info({ port: PORT, interface: BIND_ADDRESS }, "Server ready to accept connections.");
    registerUncaughtExceptionHandler();
  });

  logger.info(
    { build: process.env["BUILD_INFO"], nodeEnv: process.env.NODE_ENV },
    "Startup complete."
  );
}

function cleanup() {
  logger.info("Shutting down...");

  try {
    getDatabase().$client.close();
    logger.info("Closed sqlite database.");
  } catch {}

  process.exit(0);
}

process.on("SIGINT", cleanup);

function registerUncaughtExceptionHandler() {
  process.on("uncaughtException", (err: any) => {
    logger.error({ err, stack: err?.stack }, "UNCAUGHT EXCEPTION. Please report this error trace.");
  });
  process.on("unhandledRejection", (err: any) => {
    logger.error(
      { err, stack: err?.stack },
      "UNCAUGHT PROMISE REJECTION. Please report this error trace."
    );
  });
}

/**
 * Attepts to collect information about the current build from either the
 * environment or the git repo used to build the image (only works if not
 * .dockerignore'd). If you're running a sekrit club fork, you can no-op this
 * function and set the BUILD_INFO env var manually, though I would prefer you
 * didn't set it to something misleading.
 */
async function setBuildInfo() {
  try {
    const promises = [
      Bun.$`git rev-parse --short HEAD`.text(),
      Bun.$`git rev-parse --abbrev-ref HEAD`.text(),
      Bun.$`git config --get remote.origin.url`.text(),
      Bun.$`git status --porcelain`.text(),
    ].map((p) => p.then((s) => s.trim()));

    let [sha, branch, remote, status] = await Promise.all(promises);

    const repo = (remote.match(/.*[\/:]([\w-]+)\/([\w\-.]+?)(?:\.git)?$/) ?? [])
      .slice(-2)
      .join("/");

    const changes =
      // ignore Dockerfile changes since that's how the user deploys the app
      status.split("\n").filter((line) => !line.endsWith("Dockerfile") && line).length > 0;

    const build = `${sha}${changes ? " (modified)" : ""} (${branch}@${repo})`;
    process.env.BUILD_INFO = build;
    logger.info({ build, status, changes }, "Got build info from Git.");
  } catch (error: any) {
    logger.error(
      {
        error,
        stdout: error.stdout?.toString(),
        stderr: error.stderr?.toString(),
      },
      "Failed to get commit SHA.",
      error
    );
    process.env.BUILD_INFO = "unknown";
  }
}

start();
