import { logger } from "@/logger";
import type { OpenAIChatCompletionStreamEvent } from "../index";

export type GoogleAICompletionResponse = {
  candidates: {
    content: { parts: [{ text: string }]; role: string };
    finishReason?: string;
    index: number;
  }[];
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
    promptTokensDetails: { modality: string; tokenCount: number }[];
    thoughtsTokenCount: number;
  };
  modelVersion: string;
  responseId: string;
};

/**
 * Given a list of Google AI chat completion events, compiles them into a single
 * finalized Google AI chat completion response so that non-streaming middleware
 * can operate on it as if it were a blocking response.
 */
export function mergeEventsForGoogleAI(
  events: OpenAIChatCompletionStreamEvent[]
): GoogleAICompletionResponse {
  const lastEvent = events[events.length - 1];

  const merged: GoogleAICompletionResponse = {
    modelVersion: "models/" + lastEvent.model,
    responseId: lastEvent.id.replace("goo-", ""),
    usageMetadata: {
      promptTokensDetails: [],
      totalTokenCount: lastEvent.usage?.total_tokens ?? 0,
      promptTokenCount: lastEvent.usage?.prompt_tokens ?? 0,
      candidatesTokenCount: lastEvent.usage?.completion_tokens ?? 0,
      thoughtsTokenCount: lastEvent.usage?.completion_tokens_details.reasoning_tokens ?? 0,
    },
    candidates: [],
  };

  merged.candidates = events.reduce(
    (acc, event) => {
      acc[0].content.parts[0].text += event.choices[0]?.delta?.content ?? "";

      if (event.choices[0]?.finish_reason) {
        acc[0].finishReason = event.choices[0].finish_reason.toUpperCase();
      }

      if (event.choices[0]?.index) {
        acc[0].index = event.choices[0].index;
      }

      return acc;
    },
    [
      { content: { parts: [{ text: "" }], role: "model" }, index: 0 },
    ] as GoogleAICompletionResponse["candidates"]
  );

  return merged;
}
