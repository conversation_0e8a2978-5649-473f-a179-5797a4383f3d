import type { LLMService, ModelFamily } from "../models";
import { KeyPool } from "./key-pool";

/**
 * The request and response format used by a model's API.
 *
 * This type defines the different API formats that the proxy can handle:
 * - `"openai"`: OpenAI-compatible API format (used by OpenAI, Groq, XAI, DeepSeek)
 * - `"openai-response"`: OpenAI Response API format
 * - `"anthropic-chat"`: Anthropic's Messages API format
 * - `"google-ai"`: Google AI's GenerativeAI API format
 *
 * The proxy can transform requests between these formats, allowing clients to
 * use one format while the backend service expects another.
 */
export type APIFormat = "openai" | "openai-response" | "anthropic-chat" | "google-ai";

export type Usages = {
  input: { tokens: number; cost: number };
  output: { tokens: number; cost: number };
  prompts: number;
};

export const defaultUsages: Usages = {
  input: { tokens: 0, cost: 0 },
  output: { tokens: 0, cost: 0 },
  prompts: 0,
};

export interface Key {
  /** The API key itself. Never log this, use `hash` instead. */
  readonly key: string;
  /** The service that this key is for. */
  service: LLMService;
  /** The model families that this key has access to. */
  modelFamilies: ModelFamily[];
  /** Whether this key is currently disabled, meaning its quota has been exceeded or it has been revoked. */
  isDisabled: boolean;
  /** Whether this key specifically has been revoked. */
  isRevoked: boolean;
  /** The reason why this key was disabled, if applicable. */
  disabledReason?: "quota" | "revoked" | "error";
  /** The time at which this key was disabled. */
  disabledAt?: number;
  /** Who or what disabled this key (e.g., 'system', 'admin', 'key-checker'). */
  disabledBy?: string;
  /** The date at which this key was added to the pool. */
  addedAt: number;
  /** The number of input tokens/cost that have been used by this key. */
  input: { tokens: number; cost: number };
  /** The number of output tokens/cost that have been used by this key. */
  output: { tokens: number; cost: number };
  /** The number of prompts that have been used by this key. */
  prompts: number;
  /** The time at which this key was last used. */
  lastUsedAt: number;
  /** The time at which this key was last checked. */
  lastCheckedAt: number;
  /** The time at which this key was first checked. */
  firstCheckedAt: number;
  /** Hash of the key, for logging and to find the key in the pool. */
  hash: string;
  /** The time at which this key was last rate limited. */
  rateLimitedAt: number;
  /** The time until which this key is rate limited. */
  rateLimitedUntil: number;
}

/*
KeyPool and KeyProvider's similarities are a relic of the old design where
there was only a single KeyPool for OpenAI keys. Now that there are multiple
supported services, the service-specific functionality has been moved to
KeyProvider and KeyPool is just a wrapper around multiple KeyProviders,
delegating to the appropriate one based on the model requested.

Existing code will continue to call methods on KeyPool, which routes them to
the appropriate KeyProvider or returns data aggregated across all KeyProviders
for service-agnostic functionality.
*/

export interface KeyProvider<T extends Key = Key> {
  readonly service: LLMService;
  init(): void;
  addKey(data: { key: string; isStartup?: boolean }): boolean;
  removeKey(hash: string): boolean;
  get(model: string): T;
  list(): Omit<T, "key">[];
  disable(key: T, reason?: "quota" | "revoked" | "error", disabledBy?: string): void;
  update(hash: string, update: Partial<T>): void;
  available(): number;
  incrementUsage(hash: string, model: string, inputTokens: number, outputTokens: number): void;
  getLockoutPeriod(model: ModelFamily): number;
  markRateLimited(hash: string): void;
  recheck(): void;
}

export function createGenericGetLockoutPeriod<T extends Key>(getKeys: () => T[]) {
  return function (this: unknown, family?: ModelFamily): number {
    const keys = getKeys();
    const activeKeys = keys.filter(
      (k) => !k.isDisabled && (!family || k.modelFamilies.includes(family))
    );

    if (activeKeys.length === 0) return 0;

    const now = Date.now();
    const rateLimitedKeys = activeKeys.filter((k) => now < k.rateLimitedUntil);
    const anyNotRateLimited = rateLimitedKeys.length < activeKeys.length;

    if (anyNotRateLimited) return 0;

    return Math.min(...activeKeys.map((k) => k.rateLimitedUntil - now));
  };
}

export const keyPool = new KeyPool();
export type { AnthropicKey } from "./anthropic/provider";
export type { GoogleAIKey } from "./google-ai/provider";
export type { OpenAIKey } from "./openai/provider";
export type { DeepSeekKey } from "./deepseek/provider";
export type { XAIKey } from "./xai/provider";
export type { GroqKey } from "./groq/provider";
