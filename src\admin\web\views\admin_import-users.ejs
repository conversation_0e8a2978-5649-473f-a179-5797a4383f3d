<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "Import Users" }) %>
  </head>

  <body class="bg-background font-mono text-white">
    <div class="mx-auto flex min-h-svh w-full max-w-7xl flex-col gap-4 p-4">
      <h1 class="text-center text-3xl font-bold">Import Users</h1>

      <div class="flex flex-1 flex-col items-center justify-center gap-4">
        <div class="card p-4">
          <h2 class="mb-4 text-2xl font-bold">Import Format</h2>

          <p class="mb-4">
            Import users from JSON. The JSON should be an array of objects under the key
            <code>users</code>. Each object should have the following fields:
          </p>

          <ul class="mb-4 list-disc space-y-2 pl-5">
            <li><code>token</code> (required): a unique identifier for the user</li>
            <li><code>nickname</code> (optional): a nickname for the user, max 80 chars</li>
            <li><code>ip</code> (optional): IP addresses the user has connected from</li>
            <li>
              <code>type</code> (optional): either <code>normal</code> or
              <code>special</code>
            </li>
            <li>
              <code>promptCount</code> (optional): the number of times the user has sent a prompt
            </li>
            <li>
              <code>tokenCounts</code> (optional): the number of tokens the user has consumed. This
              should be an object with keys <code>turbo</code>, <code>gpt4</code>, and
              <code>claude</code>.
            </li>
            <li>
              <code>tokenLimits</code> (optional): the number of tokens the user can consume. This
              should be an object with keys <code>turbo</code>, <code>gpt4</code>, and
              <code>claude</code>.
            </li>
            <li><code>createdAt</code> (optional): the timestamp when the user was created</li>
            <li><code>disabledAt</code> (optional): the timestamp when the user was disabled</li>
            <li><code>disabledReason</code> (optional): the reason the user was disabled</li>
          </ul>
          <p>
            If a user with the same token already exists, the existing user will be updated with the
            new values.
          </p>
        </div>

        <div class="card w-full p-4">
          <h2 class="mb-4 text-2xl font-bold">Upload JSON File</h2>
          <form
            action="/admin/manage/import-users?_csrf=<%= csrfToken %>"
            method="post"
            enctype="multipart/form-data"
            class="space-y-4"
          >
            <div>
              <label for="users" class="text-accent mb-2 block font-bold">
                <span class="mr-2 inline-block text-base">📁</span> Select JSON File
              </label>
              <input
                type="file"
                name="users"
                id="users"
                class="bg-background-tertiary border-border w-full rounded-lg border p-2 text-white"
              />
            </div>

            <button type="submit" class="primary-button w-full">
              <span class="mr-2 inline-block text-base">⬆️</span> Import Users
            </button>
          </form>
        </div>
      </div>

      <!-- prettier-ignore -->
      <%- include("partials/admin-footer", { url: "/admin/manage/list-users", text: "Back to Users" }) %>
    </div>
  </body>
</html>
