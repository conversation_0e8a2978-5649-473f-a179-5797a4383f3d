import { drizzle } from "drizzle-orm/bun-sqlite";
import { migrate } from "drizzle-orm/bun-sqlite/migrator";
import { DefaultLogger, type LogWriter } from "drizzle-orm/logger";

import * as schema from "./schema";

import { logger as baseLogger } from "@/logger";

class CustomLogger implements LogWriter {
  #logger = baseLogger.child({ module: "database" });

  write(message: string) {
    this.#logger.debug(message);
  }
}

const logger = new DefaultLogger({ writer: new CustomLogger() });

export { drizzle, migrate, schema, logger };
