import { AxiosError } from "axios";

import { type DeepSeekModelFamily, getDeepSeekModelFamily } from "../../models";
import { getAxiosInstance } from "../../network";
import { KeyCheckerBase } from "../key-checker-base";
import type { DeepSeekK<PERSON>, DeepSeekKeyProvider } from "./provider";

const axios = getAxiosInstance();

const MIN_CHECK_INTERVAL = 3 * 1000; // 3 seconds
const KEY_CHECK_PERIOD = 6 * 60 * 60 * 1000; // 3 hours
const KEY_CHECK_TIMEOUT = 10 * 1000; // 10 seconds
const GET_MODELS_URL = "https://api.deepseek.com/v1/models";
const POST_CHAT_COMPLETIONS_URL = "https://api.deepseek.com/beta/chat/completions";

type GetModelsResponse = {
  data: [{ id: string }];
};

type UpdateFn = typeof DeepSeekKeyProvider.prototype.update;
type DisableFn = typeof DeepSeekKeyProvider.prototype.disable;

export class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends KeyCheckerBase<DeepSeekKey> {
  constructor(keys: DeepSeekKey[], updateKey: UpdateFn, disableKey: DisableFn) {
    super(keys, {
      service: "deepseek",
      keyCheckPeriod: KEY_CHECK_PERIOD,
      minCheckInterval: MIN_CHECK_INTERVAL,
      recurringChecksEnabled: true,
      updateKey,
      disableKey,
    });
  }

  private async getProvisionedModels(key: DeepSeekKey): Promise<DeepSeekModelFamily[]> {
    const opts = { headers: DeepSeekKeyChecker.getHeaders(key) };
    const { data } = await axios.get<GetModelsResponse>(GET_MODELS_URL, opts);
    const ids = new Set<string>();
    const families = new Set<DeepSeekModelFamily>();
    data.data.forEach(({ id }) => {
      ids.add(id);
      families.add(getDeepSeekModelFamily(id));
    });

    this.updateKey(key.hash, {
      modelIds: Array.from(ids),
      modelFamilies: Array.from(families),
    });

    return key.modelFamilies;
  }

  private async testLiveness(key: DeepSeekKey): Promise<void> {
    const payload = {
      model: "deepseek-chat",
      max_tokens: 1,
      messages: [{ role: "user", content: "hello" }],
    };

    await axios.post<DeepSeekError>(POST_CHAT_COMPLETIONS_URL, payload, {
      timeout: KEY_CHECK_TIMEOUT,
      validateStatus: (status) => status === 200,
      headers: DeepSeekKeyChecker.getHeaders(key),
    });
  }

  protected async testKeyOrFail(key: DeepSeekKey) {
    // We only need to check for provisioned models on the initial check.
    const isInitialCheck = !key.firstCheckedAt;

    if (isInitialCheck) {
      const [provisionedModels] = await Promise.all([
        this.getProvisionedModels(key),
        this.testLiveness(key),
      ]);

      this.updateKey(key.hash, {
        modelFamilies: provisionedModels,
        firstCheckedAt: Date.now(),
      });
    } else {
      await this.testLiveness(key);
      this.updateKey(key.hash, {});
    }

    this.log.info(
      { key: key.hash, models: key.modelFamilies, snapshots: key.modelIds },
      "Checked key."
    );
  }

  protected handleAxiosError(key: DeepSeekKey, error: AxiosError): void {
    if (error.response && DeepSeekKeyChecker.errorIsDeepSeekError(error)) {
      const { status, data } = error.response;
      const { code, message, type } = error.response.data.error;

      /**
       * Currenly Deepseek doesn't have a ratelimit for the API so we
       * just need to check for invalid or insufficient balance errors
       * and disable the key in those cases.
       * INFO: https://api-docs.deepseek.com/quick_start/rate_limit
       *
       * NOTE: There is a special case for 429 errors, which is a rate limit error
       * when we have too many failed requests for checking revoked keys. We don't
       * want to disable the key in this case, but we want to in this case, but
       * we want to wait for a minute before trying again.
       */
      switch (true) {
        case status === 401 || message.includes("no such user"): {
          this.log.warn(
            { key: key.hash, error: data },
            "Key is invalid or revoked. Disabling key."
          );
          this.disableKey(key, "revoked", "key-checker");
          break;
        }

        case status === 402 && data.error.message.includes("Insufficient Balance"): {
          this.log.warn(
            { key: key.hash, rateLimitType: data.error.type, error: data },
            "Key returned 402 error due to insufficient balance. Disabling key."
          );
          this.disableKey(key, "quota", "key-checker");
          this.updateKey(key.hash, {
            isOverQuota: true,
          });
          break;
        }

        case status === 429 && data.error_msg?.includes("Multiple 401 errors detected."): {
          this.log.warn(
            { key: key.hash, error: data },
            "Key returned 429 error due to too many failed requests. Waiting for a minute before trying again."
          );

          const oneMinute = 10 * 1000;
          const next = Date.now() - (KEY_CHECK_PERIOD - oneMinute);
          this.updateKey(key.hash, { lastCheckedAt: next });
          break;
        }

        default: {
          this.log.error(
            { key: key.hash, status, code, message, type },
            "Encountered unexpected error status while checking key. This may indicate a change in the API; please report this."
          );
          return this.updateKey(key.hash, { lastCheckedAt: Date.now() });
        }
      }
      return;
    }

    this.log.error(
      { key: key.hash, error: error.message },
      "Network error while checking key; trying this key again in a minute."
    );
    const oneMinute = 10 * 1000;
    const next = Date.now() - (KEY_CHECK_PERIOD - oneMinute);
    return this.updateKey(key.hash, { lastCheckedAt: next });
  }

  static errorIsDeepSeekError(error: AxiosError): error is AxiosError<DeepSeekError> {
    const data = error.response?.data as any;
    return data?.error?.type;
  }

  static getHeaders(key: DeepSeekKey) {
    return {
      Authorization: `Bearer ${key.key}`,
      "Content-Type": "application/json",
    };
  }
}

type DeepSeekError = {
  error: { type: string; code: string; param: unknown; message: string };
  // This error only appear if we have too many failed requests
  error_msg: string | null;
};
